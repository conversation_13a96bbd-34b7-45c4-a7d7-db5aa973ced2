{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Dash\\\\src\\\\components\\\\PropertyChart.js\";\nimport React from 'react';\nimport { Paper, Typography, Box } from '@mui/material';\nimport { BarChart } from '@mui/x-charts/BarChart';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PropertyChart = ({\n  title,\n  data,\n  height = 300,\n  isComparison = false\n}) => {\n  // Prepare data for the chart\n  const prepareChartData = () => {\n    if (isComparison) {\n      // For comparison charts (like Property Quick Filters)\n      return {\n        xAxis: [{\n          scaleType: 'band',\n          data: data.map(item => item.month),\n          categoryGapRatio: 0.3,\n          barGapRatio: 0.1\n        }],\n        series: [{\n          data: data.map(item => item.rent),\n          label: 'Rent',\n          color: '#e0e0e0'\n        }, {\n          data: data.map(item => item.sell),\n          label: 'Sell',\n          color: '#2196f3'\n        }]\n      };\n    } else {\n      // For single series charts (like Property Type) - show individual bars, not aggregated\n      // Create simplified labels for x-axis to match screenshot\n      const xAxisLabels = data.map((item, index) => {\n        const shortLabels = {\n          'Condo': 'Condo',\n          'Single Family': 'Single Family',\n          'Apartment': 'Apartment',\n          'Land': 'Land'\n        };\n        return shortLabels[item.type] || item.type;\n      });\n      return {\n        xAxis: [{\n          scaleType: 'band',\n          data: xAxisLabels,\n          categoryGapRatio: 0.1\n        }],\n        series: [{\n          data: data.map(item => item.value),\n          // Highlight the apartment bar that has value 45\n          color: data.map((item, index) => item.type === 'Apartment' && item.value === 45 ? '#2196f3' : '#e0e0e0')\n        }]\n      };\n    }\n  };\n  const chartConfig = prepareChartData();\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 3,\n      height: height + 80,\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n      borderRadius: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      component: \"h2\",\n      sx: {\n        mb: 2,\n        fontWeight: 600,\n        color: 'text.primary'\n      },\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        height: height\n      },\n      children: /*#__PURE__*/_jsxDEV(BarChart, {\n        xAxis: chartConfig.xAxis,\n        series: chartConfig.series,\n        width: undefined,\n        height: height,\n        margin: {\n          left: 50,\n          right: 50,\n          top: 20,\n          bottom: 50\n        },\n        colors: isComparison ? ['#e0e0e0', '#2196f3'] : undefined,\n        slotProps: {\n          legend: isComparison ? {\n            direction: 'row',\n            position: {\n              vertical: 'top',\n              horizontal: 'right'\n            }\n          } : undefined\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_c = PropertyChart;\nexport default PropertyChart;\nvar _c;\n$RefreshReg$(_c, \"PropertyChart\");", "map": {"version": 3, "names": ["React", "Paper", "Typography", "Box", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "PropertyChart", "title", "data", "height", "isComparison", "prepareChartData", "xAxis", "scaleType", "map", "item", "month", "categoryGapRatio", "barGapRatio", "series", "rent", "label", "color", "sell", "xAxisLabels", "index", "<PERSON><PERSON><PERSON><PERSON>", "type", "value", "chartConfig", "sx", "p", "boxShadow", "borderRadius", "children", "variant", "component", "mb", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "undefined", "margin", "left", "right", "top", "bottom", "colors", "slotProps", "legend", "direction", "position", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Dash/src/components/PropertyChart.js"], "sourcesContent": ["import React from 'react';\nimport { Paper, Typography, Box } from '@mui/material';\nimport { Bar<PERSON>hart } from '@mui/x-charts/BarChart';\n\nconst PropertyChart = ({ title, data, height = 300, isComparison = false }) => {\n  // Prepare data for the chart\n  const prepareChartData = () => {\n    if (isComparison) {\n      // For comparison charts (like Property Quick Filters)\n      return {\n        xAxis: [{\n          scaleType: 'band',\n          data: data.map(item => item.month),\n          categoryGapRatio: 0.3,\n          barGapRatio: 0.1,\n        }],\n        series: [\n          {\n            data: data.map(item => item.rent),\n            label: 'Rent',\n            color: '#e0e0e0',\n          },\n          {\n            data: data.map(item => item.sell),\n            label: 'Sell',\n            color: '#2196f3',\n          },\n        ],\n      };\n    } else {\n      // For single series charts (like Property Type) - show individual bars, not aggregated\n      // Create simplified labels for x-axis to match screenshot\n      const xAxisLabels = data.map((item, index) => {\n        const shortLabels = {\n          'Condo': 'Condo',\n          'Single Family': 'Single Family',\n          'Apartment': 'Apartment',\n          'Land': 'Land'\n        };\n        return shortLabels[item.type] || item.type;\n      });\n\n      return {\n        xAxis: [{\n          scaleType: 'band',\n          data: xAxisLabels,\n          categoryGapRatio: 0.1,\n        }],\n        series: [\n          {\n            data: data.map(item => item.value),\n            // Highlight the apartment bar that has value 45\n            color: data.map((item, index) =>\n              (item.type === 'Apartment' && item.value === 45) ? '#2196f3' : '#e0e0e0'\n            ),\n          },\n        ],\n      };\n    }\n  };\n\n  const chartConfig = prepareChartData();\n\n  return (\n    <Paper \n      sx={{ \n        p: 3, \n        height: height + 80,\n        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n        borderRadius: 2,\n      }}\n    >\n      <Typography \n        variant=\"h6\" \n        component=\"h2\" \n        sx={{ \n          mb: 2,\n          fontWeight: 600,\n          color: 'text.primary'\n        }}\n      >\n        {title}\n      </Typography>\n      <Box sx={{ width: '100%', height: height }}>\n        <BarChart\n          xAxis={chartConfig.xAxis}\n          series={chartConfig.series}\n          width={undefined}\n          height={height}\n          margin={{\n            left: 50,\n            right: 50,\n            top: 20,\n            bottom: 50,\n          }}\n          colors={isComparison ? ['#e0e0e0', '#2196f3'] : undefined}\n          slotProps={{\n            legend: isComparison ? {\n              direction: 'row',\n              position: { vertical: 'top', horizontal: 'right' },\n            } : undefined,\n          }}\n        />\n      </Box>\n    </Paper>\n  );\n};\n\nexport default PropertyChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,UAAU,EAAEC,GAAG,QAAQ,eAAe;AACtD,SAASC,QAAQ,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,KAAK;EAAEC,IAAI;EAAEC,MAAM,GAAG,GAAG;EAAEC,YAAY,GAAG;AAAM,CAAC,KAAK;EAC7E;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAID,YAAY,EAAE;MAChB;MACA,OAAO;QACLE,KAAK,EAAE,CAAC;UACNC,SAAS,EAAE,MAAM;UACjBL,IAAI,EAAEA,IAAI,CAACM,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC;UAClCC,gBAAgB,EAAE,GAAG;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;QACFC,MAAM,EAAE,CACN;UACEX,IAAI,EAAEA,IAAI,CAACM,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACK,IAAI,CAAC;UACjCC,KAAK,EAAE,MAAM;UACbC,KAAK,EAAE;QACT,CAAC,EACD;UACEd,IAAI,EAAEA,IAAI,CAACM,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACQ,IAAI,CAAC;UACjCF,KAAK,EAAE,MAAM;UACbC,KAAK,EAAE;QACT,CAAC;MAEL,CAAC;IACH,CAAC,MAAM;MACL;MACA;MACA,MAAME,WAAW,GAAGhB,IAAI,CAACM,GAAG,CAAC,CAACC,IAAI,EAAEU,KAAK,KAAK;QAC5C,MAAMC,WAAW,GAAG;UAClB,OAAO,EAAE,OAAO;UAChB,eAAe,EAAE,eAAe;UAChC,WAAW,EAAE,WAAW;UACxB,MAAM,EAAE;QACV,CAAC;QACD,OAAOA,WAAW,CAACX,IAAI,CAACY,IAAI,CAAC,IAAIZ,IAAI,CAACY,IAAI;MAC5C,CAAC,CAAC;MAEF,OAAO;QACLf,KAAK,EAAE,CAAC;UACNC,SAAS,EAAE,MAAM;UACjBL,IAAI,EAAEgB,WAAW;UACjBP,gBAAgB,EAAE;QACpB,CAAC,CAAC;QACFE,MAAM,EAAE,CACN;UACEX,IAAI,EAAEA,IAAI,CAACM,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACa,KAAK,CAAC;UAClC;UACAN,KAAK,EAAEd,IAAI,CAACM,GAAG,CAAC,CAACC,IAAI,EAAEU,KAAK,KACzBV,IAAI,CAACY,IAAI,KAAK,WAAW,IAAIZ,IAAI,CAACa,KAAK,KAAK,EAAE,GAAI,SAAS,GAAG,SACjE;QACF,CAAC;MAEL,CAAC;IACH;EACF,CAAC;EAED,MAAMC,WAAW,GAAGlB,gBAAgB,CAAC,CAAC;EAEtC,oBACEN,OAAA,CAACL,KAAK;IACJ8B,EAAE,EAAE;MACFC,CAAC,EAAE,CAAC;MACJtB,MAAM,EAAEA,MAAM,GAAG,EAAE;MACnBuB,SAAS,EAAE,2BAA2B;MACtCC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBAEF7B,OAAA,CAACJ,UAAU;MACTkC,OAAO,EAAC,IAAI;MACZC,SAAS,EAAC,IAAI;MACdN,EAAE,EAAE;QACFO,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,GAAG;QACfhB,KAAK,EAAE;MACT,CAAE;MAAAY,QAAA,EAED3B;IAAK;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACbrC,OAAA,CAACH,GAAG;MAAC4B,EAAE,EAAE;QAAEa,KAAK,EAAE,MAAM;QAAElC,MAAM,EAAEA;MAAO,CAAE;MAAAyB,QAAA,eACzC7B,OAAA,CAACF,QAAQ;QACPS,KAAK,EAAEiB,WAAW,CAACjB,KAAM;QACzBO,MAAM,EAAEU,WAAW,CAACV,MAAO;QAC3BwB,KAAK,EAAEC,SAAU;QACjBnC,MAAM,EAAEA,MAAO;QACfoC,MAAM,EAAE;UACNC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV,CAAE;QACFC,MAAM,EAAExC,YAAY,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,GAAGkC,SAAU;QAC1DO,SAAS,EAAE;UACTC,MAAM,EAAE1C,YAAY,GAAG;YACrB2C,SAAS,EAAE,KAAK;YAChBC,QAAQ,EAAE;cAAEC,QAAQ,EAAE,KAAK;cAAEC,UAAU,EAAE;YAAQ;UACnD,CAAC,GAAGZ;QACN;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACe,EAAA,GAtGInD,aAAa;AAwGnB,eAAeA,aAAa;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}