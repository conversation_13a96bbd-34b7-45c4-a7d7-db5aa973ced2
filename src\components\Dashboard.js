import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Breadcrumbs,
  Link,
} from '@mui/material';
import StatsCard from './StatsCard';
import PropertyChart from './PropertyChart';
import AgentsList from './AgentsList';

const Dashboard = () => {
  // Sample data for the dashboard
  const statsData = [
    { title: 'Total Properties', value: '320,000', color: '#e3f2fd' },
    { title: 'Total Agents', value: '175', color: '#f3e5f5' },
    { title: 'Total Customers', value: '10,500', color: '#e8f5e8' },
    { title: 'Total Sales', value: '50', color: '#fff3e0' },
  ];

  const propertyTypeData = [
    { type: 'Condo', value: 15 },
    { type: 'Single Family', value: 25 },
    { type: 'Condo', value: 20 },
    { type: 'Single Family', value: 18 },
    { type: 'Apartment', value: 45 },
    { type: 'Single Family', value: 22 },
    { type: 'Condo', value: 16 },
    { type: 'Single Family', value: 28 },
    { type: 'Condo', value: 19 },
    { type: 'Land', value: 12 },
    { type: 'Condo', value: 21 },
    { type: 'Land', value: 14 },
  ];

  const quickFiltersData = [
    { month: 'Jan', rent: 25, sell: 15 },
    { month: 'Feb', rent: 20, sell: 18 },
    { month: 'Mar', rent: 22, sell: 16 },
    { month: 'Apr', rent: 28, sell: 20 },
    { month: 'May', rent: 45, sell: 25 },
    { month: 'Jun', rent: 30, sell: 22 },
    { month: 'Jul', rent: 26, sell: 19 },
    { month: 'Aug', rent: 24, sell: 17 },
    { month: 'Sep', rent: 27, sell: 21 },
    { month: 'Oct', rent: 23, sell: 18 },
    { month: 'Nov', rent: 29, sell: 23 },
    { month: 'Dec', rent: 31, sell: 25 },
  ];

  const recentAgents = [
    {
      name: 'Sarah Thompson',
      email: 'sarah.thompson@realty...',
      role: 'Agent',
      avatar: '/api/placeholder/40/40',
    },
    {
      name: 'Stephen Snop',
      email: '<EMAIL>',
      role: 'Agent',
      avatar: '/api/placeholder/40/40',
    },
    {
      name: 'Emily Johnson',
      email: 'sarah.thompson@realty...',
      role: 'Agent',
      avatar: '/api/placeholder/40/40',
    },
    {
      name: 'John Smith',
      email: '<EMAIL>',
      role: 'Agent',
      avatar: '/api/placeholder/40/40',
    },
    {
      name: 'Michael Brown',
      email: '<EMAIL>',
      role: 'Agent',
      avatar: '/api/placeholder/40/40',
    },
    {
      name: 'Sarah Davis',
      email: 'sarah.davis@example...',
      role: 'Agent',
      avatar: '/api/placeholder/40/40',
    },
    {
      name: 'Jessica Taylor',
      email: 'jessica.taylor@realtyco...',
      role: 'Agent',
      avatar: '/api/placeholder/40/40',
    },
    {
      name: 'Daniel Miller',
      email: 'daniel.miller@realtyco...',
      role: 'Agent',
      avatar: '/api/placeholder/40/40',
    },
  ];

  return (
    <Box sx={{ backgroundColor: '#f0f4f8', minHeight: '100vh', py: 3 }}>
      <Container maxWidth="xl">
        {/* Breadcrumbs */}
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 3 }}>
          <Link underline="hover" color="inherit" href="/">
            Dashboard
          </Link>
          <Typography color="primary">Dashboard</Typography>
        </Breadcrumbs>

        <Grid container spacing={3}>
          {/* Stats Cards */}
          <Grid item xs={12}>
            <Grid container spacing={3}>
              {statsData.map((stat, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <StatsCard
                    title={stat.title}
                    value={stat.value}
                    backgroundColor={stat.color}
                  />
                </Grid>
              ))}
            </Grid>
          </Grid>

          {/* Charts Section */}
          <Grid item xs={12} md={8}>
            <Grid container spacing={3}>
              {/* Property Type Chart */}
              <Grid item xs={12}>
                <PropertyChart
                  title="Property Type"
                  data={propertyTypeData}
                  height={300}
                />
              </Grid>

              {/* Property Quick Filters Chart */}
              <Grid item xs={12}>
                <PropertyChart
                  title="Property Quick Filters"
                  data={quickFiltersData}
                  height={300}
                  isComparison={true}
                />
              </Grid>
            </Grid>
          </Grid>

          {/* Recent Registered Agents */}
          <Grid item xs={12} md={4}>
            <AgentsList agents={recentAgents} />
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default Dashboard;
