{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Dash\\\\src\\\\components\\\\AllAgents.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Container, Typography, Breadcrumbs, Link, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, InputAdornment, Chip, IconButton, TablePagination, Avatar, Checkbox } from '@mui/material';\nimport { Search as SearchIcon, Edit as EditIcon } from '@mui/icons-material';\nimport EditAgentModal from './EditAgentModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllAgents = () => {\n  _s();\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selected, setSelected] = useState([]);\n  const [editModalOpen, setEditModalOpen] = useState(false);\n  const [selectedAgent, setSelectedAgent] = useState(null);\n\n  // Sample agent data\n  const agentsData = [{\n    id: 1,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 2,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 3,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 4,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 5,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 6,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 7,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 8,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 9,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'No',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 10,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'No',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }];\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n  const handleChangeRowsPerPage = event => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n  const handleSearchChange = event => {\n    setSearchTerm(event.target.value);\n  };\n  const handleSelectAllClick = event => {\n    if (event.target.checked) {\n      const newSelected = agentsData.map(n => n.id);\n      setSelected(newSelected);\n      return;\n    }\n    setSelected([]);\n  };\n  const handleClick = (event, id) => {\n    const selectedIndex = selected.indexOf(id);\n    let newSelected = [];\n    if (selectedIndex === -1) {\n      newSelected = newSelected.concat(selected, id);\n    } else if (selectedIndex === 0) {\n      newSelected = newSelected.concat(selected.slice(1));\n    } else if (selectedIndex === selected.length - 1) {\n      newSelected = newSelected.concat(selected.slice(0, -1));\n    } else if (selectedIndex > 0) {\n      newSelected = newSelected.concat(selected.slice(0, selectedIndex), selected.slice(selectedIndex + 1));\n    }\n    setSelected(newSelected);\n  };\n  const isSelected = id => selected.indexOf(id) !== -1;\n  const handleEditAgent = agent => {\n    setSelectedAgent(agent);\n    setEditModalOpen(true);\n  };\n  const handleCloseEditModal = () => {\n    setEditModalOpen(false);\n    setSelectedAgent(null);\n  };\n  const handleSaveAgent = updatedAgentData => {\n    // Here you would typically update the agent data in your backend/state\n    console.log('Saving agent data:', updatedAgentData);\n    // For now, just log the data - you can implement the actual save logic later\n  };\n  const getInitials = name => {\n    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);\n  };\n  const getAvatarColor = name => {\n    const colors = ['#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4caf50', '#8bc34a', '#cddc39'];\n    const index = name.charCodeAt(0) % colors.length;\n    return colors[index];\n  };\n  const getStatusColor = status => {\n    switch (status.toLowerCase()) {\n      case 'verified':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'rejected':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const filteredAgents = agentsData.filter(agent => agent.name.toLowerCase().includes(searchTerm.toLowerCase()) || agent.email.toLowerCase().includes(searchTerm.toLowerCase()) || agent.phone.includes(searchTerm));\n  const paginatedAgents = filteredAgents.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      backgroundColor: '#f5f5f5',\n      minHeight: '100vh',\n      py: 3\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: [/*#__PURE__*/_jsxDEV(Breadcrumbs, {\n        \"aria-label\": \"breadcrumb\",\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          underline: \"hover\",\n          color: \"inherit\",\n          href: \"/\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"primary\",\n          children: \"All Agents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Search by username, email\",\n          value: searchTerm,\n          onChange: handleSearchChange,\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                color: \"action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this)\n          },\n          sx: {\n            maxWidth: 400,\n            backgroundColor: 'white',\n            '& .MuiOutlinedInput-root': {\n              borderRadius: 2\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          width: '100%',\n          mb: 2,\n          borderRadius: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            sx: {\n              minWidth: 750\n            },\n            \"aria-labelledby\": \"tableTitle\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  backgroundColor: '#f8f9fa'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  padding: \"checkbox\",\n                  children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                    color: \"primary\",\n                    indeterminate: selected.length > 0 && selected.length < agentsData.length,\n                    checked: agentsData.length > 0 && selected.length === agentsData.length,\n                    onChange: handleSelectAllClick\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Phone Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Commission\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Running Lead\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Joining Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: paginatedAgents.map((agent, index) => {\n                const isItemSelected = isSelected(agent.id);\n                const labelId = `enhanced-table-checkbox-${index}`;\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  hover: true,\n                  onClick: event => handleClick(event, agent.id),\n                  role: \"checkbox\",\n                  \"aria-checked\": isItemSelected,\n                  tabIndex: -1,\n                  selected: isItemSelected,\n                  sx: {\n                    cursor: 'pointer'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    padding: \"checkbox\",\n                    children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                      color: \"primary\",\n                      checked: isItemSelected,\n                      inputProps: {\n                        'aria-labelledby': labelId\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    component: \"th\",\n                    id: labelId,\n                    scope: \"row\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                        sx: {\n                          bgcolor: getAvatarColor(agent.name),\n                          width: 32,\n                          height: 32,\n                          fontSize: '0.875rem'\n                        },\n                        children: getInitials(agent.name)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 328,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 500\n                        },\n                        children: agent.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 338,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 327,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"primary\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: agent.phone\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: agent.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: agent.commission,\n                      color: agent.commission === 'Yes' ? 'success' : 'error',\n                      size: \"small\",\n                      sx: {\n                        minWidth: 60\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: agent.runningLead,\n                      color: getStatusColor(agent.runningLead),\n                      size: \"small\",\n                      sx: {\n                        minWidth: 80\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: agent.joinDate\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 370,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Chip, {\n                        label: agent.status,\n                        color: getStatusColor(agent.status),\n                        size: \"small\",\n                        sx: {\n                          minWidth: 80\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 376,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"primary\",\n                        onClick: () => handleEditAgent(agent),\n                        children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 387,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 382,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 23\n                  }, this)]\n                }, agent.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"Showing \", page * rowsPerPage + 1, \" to \", Math.min((page + 1) * rowsPerPage, filteredAgents.length), \" of \", filteredAgents.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n            rowsPerPageOptions: [5, 10, 25],\n            component: \"div\",\n            count: filteredAgents.length,\n            rowsPerPage: rowsPerPage,\n            page: page,\n            onPageChange: handleChangePage,\n            onRowsPerPageChange: handleChangeRowsPerPage,\n            sx: {\n              '& .MuiTablePagination-toolbar': {\n                minHeight: 'auto'\n              },\n              '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {\n                margin: 0\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 244,\n    columnNumber: 5\n  }, this);\n};\n_s(AllAgents, \"Jybuvw6V2h6Q2rZWORTqnvKxXFI=\");\n_c = AllAgents;\nexport default AllAgents;\nvar _c;\n$RefreshReg$(_c, \"AllAgents\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Container", "Typography", "Breadcrumbs", "Link", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TextField", "InputAdornment", "Chip", "IconButton", "TablePagination", "Avatar", "Checkbox", "Search", "SearchIcon", "Edit", "EditIcon", "EditAgentModal", "jsxDEV", "_jsxDEV", "AllAgents", "_s", "page", "setPage", "rowsPerPage", "setRowsPerPage", "searchTerm", "setSearchTerm", "selected", "setSelected", "editModalOpen", "setEditModalOpen", "selectedAgent", "setSelectedAgent", "agentsData", "id", "name", "phone", "email", "joinDate", "commission", "status", "runningLead", "handleChangePage", "event", "newPage", "handleChangeRowsPerPage", "parseInt", "target", "value", "handleSearchChange", "handleSelectAllClick", "checked", "newSelected", "map", "n", "handleClick", "selectedIndex", "indexOf", "concat", "slice", "length", "isSelected", "handleEditAgent", "agent", "handleCloseEditModal", "handleSaveAgent", "updatedAgentData", "console", "log", "getInitials", "split", "word", "char<PERSON>t", "join", "toUpperCase", "getAvatarColor", "colors", "index", "charCodeAt", "getStatusColor", "toLowerCase", "filteredAgents", "filter", "includes", "paginatedAgents", "sx", "backgroundColor", "minHeight", "py", "children", "max<PERSON><PERSON><PERSON>", "mb", "underline", "color", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "placeholder", "onChange", "InputProps", "startAdornment", "position", "borderRadius", "width", "min<PERSON><PERSON><PERSON>", "padding", "indeterminate", "fontWeight", "isItemSelected", "labelId", "hover", "onClick", "role", "tabIndex", "cursor", "inputProps", "component", "scope", "display", "alignItems", "gap", "bgcolor", "height", "fontSize", "variant", "label", "size", "justifyContent", "p", "Math", "min", "rowsPerPageOptions", "count", "onPageChange", "onRowsPerPageChange", "margin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Dash/src/components/AllAgents.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Breadcrumbs,\n  Link,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TextField,\n  InputAdornment,\n  Chip,\n  IconButton,\n  TablePagination,\n  Avatar,\n  Checkbox,\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Edit as EditIcon,\n} from '@mui/icons-material';\nimport EditAgentModal from './EditAgentModal';\n\nconst AllAgents = () => {\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selected, setSelected] = useState([]);\n  const [editModalOpen, setEditModalOpen] = useState(false);\n  const [selectedAgent, setSelectedAgent] = useState(null);\n\n  // Sample agent data\n  const agentsData = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 2,\n      name: '<PERSON>',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 3,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 4,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 5,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 6,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 7,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 8,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 9,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'No',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 10,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'No',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n  ];\n\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n\n  const handleChangeRowsPerPage = (event) => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n\n  const handleSearchChange = (event) => {\n    setSearchTerm(event.target.value);\n  };\n\n  const handleSelectAllClick = (event) => {\n    if (event.target.checked) {\n      const newSelected = agentsData.map((n) => n.id);\n      setSelected(newSelected);\n      return;\n    }\n    setSelected([]);\n  };\n\n  const handleClick = (event, id) => {\n    const selectedIndex = selected.indexOf(id);\n    let newSelected = [];\n\n    if (selectedIndex === -1) {\n      newSelected = newSelected.concat(selected, id);\n    } else if (selectedIndex === 0) {\n      newSelected = newSelected.concat(selected.slice(1));\n    } else if (selectedIndex === selected.length - 1) {\n      newSelected = newSelected.concat(selected.slice(0, -1));\n    } else if (selectedIndex > 0) {\n      newSelected = newSelected.concat(\n        selected.slice(0, selectedIndex),\n        selected.slice(selectedIndex + 1),\n      );\n    }\n    setSelected(newSelected);\n  };\n\n  const isSelected = (id) => selected.indexOf(id) !== -1;\n\n  const handleEditAgent = (agent) => {\n    setSelectedAgent(agent);\n    setEditModalOpen(true);\n  };\n\n  const handleCloseEditModal = () => {\n    setEditModalOpen(false);\n    setSelectedAgent(null);\n  };\n\n  const handleSaveAgent = (updatedAgentData) => {\n    // Here you would typically update the agent data in your backend/state\n    console.log('Saving agent data:', updatedAgentData);\n    // For now, just log the data - you can implement the actual save logic later\n  };\n\n  const getInitials = (name) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  const getAvatarColor = (name) => {\n    const colors = [\n      '#f44336', '#e91e63', '#9c27b0', '#673ab7',\n      '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4',\n      '#009688', '#4caf50', '#8bc34a', '#cddc39',\n    ];\n    const index = name.charCodeAt(0) % colors.length;\n    return colors[index];\n  };\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'verified':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'rejected':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const filteredAgents = agentsData.filter(agent =>\n    agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    agent.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    agent.phone.includes(searchTerm)\n  );\n\n  const paginatedAgents = filteredAgents.slice(\n    page * rowsPerPage,\n    page * rowsPerPage + rowsPerPage\n  );\n\n  return (\n    <Box sx={{ backgroundColor: '#f5f5f5', minHeight: '100vh', py: 3 }}>\n      <Container maxWidth=\"xl\">\n        {/* Breadcrumbs */}\n        <Breadcrumbs aria-label=\"breadcrumb\" sx={{ mb: 3 }}>\n          <Link underline=\"hover\" color=\"inherit\" href=\"/\">\n            Dashboard\n          </Link>\n          <Typography color=\"primary\">All Agents</Typography>\n        </Breadcrumbs>\n\n        {/* Search Bar */}\n        <Box sx={{ mb: 3 }}>\n          <TextField\n            fullWidth\n            placeholder=\"Search by username, email\"\n            value={searchTerm}\n            onChange={handleSearchChange}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon color=\"action\" />\n                </InputAdornment>\n              ),\n            }}\n            sx={{\n              maxWidth: 400,\n              backgroundColor: 'white',\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 2,\n              },\n            }}\n          />\n        </Box>\n\n        {/* Table */}\n        <Paper sx={{ width: '100%', mb: 2, borderRadius: 2 }}>\n          <TableContainer>\n            <Table sx={{ minWidth: 750 }} aria-labelledby=\"tableTitle\">\n              <TableHead>\n                <TableRow sx={{ backgroundColor: '#f8f9fa' }}>\n                  <TableCell padding=\"checkbox\">\n                    <Checkbox\n                      color=\"primary\"\n                      indeterminate={selected.length > 0 && selected.length < agentsData.length}\n                      checked={agentsData.length > 0 && selected.length === agentsData.length}\n                      onChange={handleSelectAllClick}\n                    />\n                  </TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Name</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Phone Number</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Email</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Commission</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Running Lead</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Joining Date</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Status</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {paginatedAgents.map((agent, index) => {\n                  const isItemSelected = isSelected(agent.id);\n                  const labelId = `enhanced-table-checkbox-${index}`;\n\n                  return (\n                    <TableRow\n                      hover\n                      onClick={(event) => handleClick(event, agent.id)}\n                      role=\"checkbox\"\n                      aria-checked={isItemSelected}\n                      tabIndex={-1}\n                      key={agent.id}\n                      selected={isItemSelected}\n                      sx={{ cursor: 'pointer' }}\n                    >\n                      <TableCell padding=\"checkbox\">\n                        <Checkbox\n                          color=\"primary\"\n                          checked={isItemSelected}\n                          inputProps={{\n                            'aria-labelledby': labelId,\n                          }}\n                        />\n                      </TableCell>\n                      <TableCell component=\"th\" id={labelId} scope=\"row\">\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                          <Avatar\n                            sx={{\n                              bgcolor: getAvatarColor(agent.name),\n                              width: 32,\n                              height: 32,\n                              fontSize: '0.875rem',\n                            }}\n                          >\n                            {getInitials(agent.name)}\n                          </Avatar>\n                          <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                            {agent.name}\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\" color=\"primary\" sx={{ fontWeight: 500 }}>\n                          {agent.phone}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {agent.email}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={agent.commission}\n                          color={agent.commission === 'Yes' ? 'success' : 'error'}\n                          size=\"small\"\n                          sx={{ minWidth: 60 }}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={agent.runningLead}\n                          color={getStatusColor(agent.runningLead)}\n                          size=\"small\"\n                          sx={{ minWidth: 80 }}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {agent.joinDate}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          <Chip\n                            label={agent.status}\n                            color={getStatusColor(agent.status)}\n                            size=\"small\"\n                            sx={{ minWidth: 80 }}\n                          />\n                          <IconButton\n                            size=\"small\"\n                            color=\"primary\"\n                            onClick={() => handleEditAgent(agent)}\n                          >\n                            <EditIcon fontSize=\"small\" />\n                          </IconButton>\n                        </Box>\n                      </TableCell>\n                    </TableRow>\n                  );\n                })}\n              </TableBody>\n            </Table>\n          </TableContainer>\n          \n          {/* Pagination */}\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Showing {page * rowsPerPage + 1} to {Math.min((page + 1) * rowsPerPage, filteredAgents.length)} of {filteredAgents.length}\n            </Typography>\n            <TablePagination\n              rowsPerPageOptions={[5, 10, 25]}\n              component=\"div\"\n              count={filteredAgents.length}\n              rowsPerPage={rowsPerPage}\n              page={page}\n              onPageChange={handleChangePage}\n              onRowsPerPageChange={handleChangeRowsPerPage}\n              sx={{\n                '& .MuiTablePagination-toolbar': {\n                  minHeight: 'auto',\n                },\n                '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {\n                  margin: 0,\n                },\n              }}\n            />\n          </Box>\n        </Paper>\n      </Container>\n    </Box>\n  );\n};\n\nexport default AllAgents;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,eAAe,EACfC,MAAM,EACNC,QAAQ,QACH,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAMyC,UAAU,GAAG,CACjB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,EAAE;IACNC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC3CtB,OAAO,CAACsB,OAAO,CAAC;EAClB,CAAC;EAED,MAAMC,uBAAuB,GAAIF,KAAK,IAAK;IACzCnB,cAAc,CAACsB,QAAQ,CAACH,KAAK,CAACI,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,CAAC;IAChD1B,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC;EAED,MAAM2B,kBAAkB,GAAIN,KAAK,IAAK;IACpCjB,aAAa,CAACiB,KAAK,CAACI,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;EAED,MAAME,oBAAoB,GAAIP,KAAK,IAAK;IACtC,IAAIA,KAAK,CAACI,MAAM,CAACI,OAAO,EAAE;MACxB,MAAMC,WAAW,GAAGnB,UAAU,CAACoB,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACpB,EAAE,CAAC;MAC/CN,WAAW,CAACwB,WAAW,CAAC;MACxB;IACF;IACAxB,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EAED,MAAM2B,WAAW,GAAGA,CAACZ,KAAK,EAAET,EAAE,KAAK;IACjC,MAAMsB,aAAa,GAAG7B,QAAQ,CAAC8B,OAAO,CAACvB,EAAE,CAAC;IAC1C,IAAIkB,WAAW,GAAG,EAAE;IAEpB,IAAII,aAAa,KAAK,CAAC,CAAC,EAAE;MACxBJ,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAC/B,QAAQ,EAAEO,EAAE,CAAC;IAChD,CAAC,MAAM,IAAIsB,aAAa,KAAK,CAAC,EAAE;MAC9BJ,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAC/B,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM,IAAIH,aAAa,KAAK7B,QAAQ,CAACiC,MAAM,GAAG,CAAC,EAAE;MAChDR,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAC/B,QAAQ,CAACgC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC,MAAM,IAAIH,aAAa,GAAG,CAAC,EAAE;MAC5BJ,WAAW,GAAGA,WAAW,CAACM,MAAM,CAC9B/B,QAAQ,CAACgC,KAAK,CAAC,CAAC,EAAEH,aAAa,CAAC,EAChC7B,QAAQ,CAACgC,KAAK,CAACH,aAAa,GAAG,CAAC,CAClC,CAAC;IACH;IACA5B,WAAW,CAACwB,WAAW,CAAC;EAC1B,CAAC;EAED,MAAMS,UAAU,GAAI3B,EAAE,IAAKP,QAAQ,CAAC8B,OAAO,CAACvB,EAAE,CAAC,KAAK,CAAC,CAAC;EAEtD,MAAM4B,eAAe,GAAIC,KAAK,IAAK;IACjC/B,gBAAgB,CAAC+B,KAAK,CAAC;IACvBjC,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMkC,oBAAoB,GAAGA,CAAA,KAAM;IACjClC,gBAAgB,CAAC,KAAK,CAAC;IACvBE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMiC,eAAe,GAAIC,gBAAgB,IAAK;IAC5C;IACAC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,gBAAgB,CAAC;IACnD;EACF,CAAC;EAED,MAAMG,WAAW,GAAIlC,IAAI,IAAK;IAC5B,OAAOA,IAAI,CACRmC,KAAK,CAAC,GAAG,CAAC,CACVjB,GAAG,CAACkB,IAAI,IAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC3BC,IAAI,CAAC,EAAE,CAAC,CACRC,WAAW,CAAC,CAAC,CACbf,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EAED,MAAMgB,cAAc,GAAIxC,IAAI,IAAK;IAC/B,MAAMyC,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC3C;IACD,MAAMC,KAAK,GAAG1C,IAAI,CAAC2C,UAAU,CAAC,CAAC,CAAC,GAAGF,MAAM,CAAChB,MAAM;IAChD,OAAOgB,MAAM,CAACC,KAAK,CAAC;EACtB,CAAC;EAED,MAAME,cAAc,GAAIvC,MAAM,IAAK;IACjC,QAAQA,MAAM,CAACwC,WAAW,CAAC,CAAC;MAC1B,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMC,cAAc,GAAGhD,UAAU,CAACiD,MAAM,CAACnB,KAAK,IAC5CA,KAAK,CAAC5B,IAAI,CAAC6C,WAAW,CAAC,CAAC,CAACG,QAAQ,CAAC1D,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC,IAC3DjB,KAAK,CAAC1B,KAAK,CAAC2C,WAAW,CAAC,CAAC,CAACG,QAAQ,CAAC1D,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC,IAC5DjB,KAAK,CAAC3B,KAAK,CAAC+C,QAAQ,CAAC1D,UAAU,CACjC,CAAC;EAED,MAAM2D,eAAe,GAAGH,cAAc,CAACtB,KAAK,CAC1CtC,IAAI,GAAGE,WAAW,EAClBF,IAAI,GAAGE,WAAW,GAAGA,WACvB,CAAC;EAED,oBACEL,OAAA,CAACzB,GAAG;IAAC4F,EAAE,EAAE;MAAEC,eAAe,EAAE,SAAS;MAAEC,SAAS,EAAE,OAAO;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eACjEvE,OAAA,CAACxB,SAAS;MAACgG,QAAQ,EAAC,IAAI;MAAAD,QAAA,gBAEtBvE,OAAA,CAACtB,WAAW;QAAC,cAAW,YAAY;QAACyF,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,gBACjDvE,OAAA,CAACrB,IAAI;UAAC+F,SAAS,EAAC,OAAO;UAACC,KAAK,EAAC,SAAS;UAACC,IAAI,EAAC,GAAG;UAAAL,QAAA,EAAC;QAEjD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPhF,OAAA,CAACvB,UAAU;UAACkG,KAAK,EAAC,SAAS;UAAAJ,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAGdhF,OAAA,CAACzB,GAAG;QAAC4F,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eACjBvE,OAAA,CAACb,SAAS;UACR8F,SAAS;UACTC,WAAW,EAAC,2BAA2B;UACvCpD,KAAK,EAAEvB,UAAW;UAClB4E,QAAQ,EAAEpD,kBAAmB;UAC7BqD,UAAU,EAAE;YACVC,cAAc,eACZrF,OAAA,CAACZ,cAAc;cAACkG,QAAQ,EAAC,OAAO;cAAAf,QAAA,eAC9BvE,OAAA,CAACL,UAAU;gBAACgF,KAAK,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAEpB,CAAE;UACFb,EAAE,EAAE;YACFK,QAAQ,EAAE,GAAG;YACbJ,eAAe,EAAE,OAAO;YACxB,0BAA0B,EAAE;cAC1BmB,YAAY,EAAE;YAChB;UACF;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNhF,OAAA,CAACpB,KAAK;QAACuF,EAAE,EAAE;UAAEqB,KAAK,EAAE,MAAM;UAAEf,EAAE,EAAE,CAAC;UAAEc,YAAY,EAAE;QAAE,CAAE;QAAAhB,QAAA,gBACnDvE,OAAA,CAAChB,cAAc;UAAAuF,QAAA,eACbvE,OAAA,CAACnB,KAAK;YAACsF,EAAE,EAAE;cAAEsB,QAAQ,EAAE;YAAI,CAAE;YAAC,mBAAgB,YAAY;YAAAlB,QAAA,gBACxDvE,OAAA,CAACf,SAAS;cAAAsF,QAAA,eACRvE,OAAA,CAACd,QAAQ;gBAACiF,EAAE,EAAE;kBAAEC,eAAe,EAAE;gBAAU,CAAE;gBAAAG,QAAA,gBAC3CvE,OAAA,CAACjB,SAAS;kBAAC2G,OAAO,EAAC,UAAU;kBAAAnB,QAAA,eAC3BvE,OAAA,CAACP,QAAQ;oBACPkF,KAAK,EAAC,SAAS;oBACfgB,aAAa,EAAElF,QAAQ,CAACiC,MAAM,GAAG,CAAC,IAAIjC,QAAQ,CAACiC,MAAM,GAAG3B,UAAU,CAAC2B,MAAO;oBAC1ET,OAAO,EAAElB,UAAU,CAAC2B,MAAM,GAAG,CAAC,IAAIjC,QAAQ,CAACiC,MAAM,KAAK3B,UAAU,CAAC2B,MAAO;oBACxEyC,QAAQ,EAAEnD;kBAAqB;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZhF,OAAA,CAACjB,SAAS;kBAACoF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7EhF,OAAA,CAACjB,SAAS;kBAACoF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrFhF,OAAA,CAACjB,SAAS;kBAACoF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9EhF,OAAA,CAACjB,SAAS;kBAACoF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnFhF,OAAA,CAACjB,SAAS;kBAACoF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrFhF,OAAA,CAACjB,SAAS;kBAACoF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrFhF,OAAA,CAACjB,SAAS;kBAACoF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZhF,OAAA,CAAClB,SAAS;cAAAyF,QAAA,EACPL,eAAe,CAAC/B,GAAG,CAAC,CAACU,KAAK,EAAEc,KAAK,KAAK;gBACrC,MAAMkC,cAAc,GAAGlD,UAAU,CAACE,KAAK,CAAC7B,EAAE,CAAC;gBAC3C,MAAM8E,OAAO,GAAG,2BAA2BnC,KAAK,EAAE;gBAElD,oBACE3D,OAAA,CAACd,QAAQ;kBACP6G,KAAK;kBACLC,OAAO,EAAGvE,KAAK,IAAKY,WAAW,CAACZ,KAAK,EAAEoB,KAAK,CAAC7B,EAAE,CAAE;kBACjDiF,IAAI,EAAC,UAAU;kBACf,gBAAcJ,cAAe;kBAC7BK,QAAQ,EAAE,CAAC,CAAE;kBAEbzF,QAAQ,EAAEoF,cAAe;kBACzB1B,EAAE,EAAE;oBAAEgC,MAAM,EAAE;kBAAU,CAAE;kBAAA5B,QAAA,gBAE1BvE,OAAA,CAACjB,SAAS;oBAAC2G,OAAO,EAAC,UAAU;oBAAAnB,QAAA,eAC3BvE,OAAA,CAACP,QAAQ;sBACPkF,KAAK,EAAC,SAAS;sBACf1C,OAAO,EAAE4D,cAAe;sBACxBO,UAAU,EAAE;wBACV,iBAAiB,EAAEN;sBACrB;oBAAE;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZhF,OAAA,CAACjB,SAAS;oBAACsH,SAAS,EAAC,IAAI;oBAACrF,EAAE,EAAE8E,OAAQ;oBAACQ,KAAK,EAAC,KAAK;oBAAA/B,QAAA,eAChDvE,OAAA,CAACzB,GAAG;sBAAC4F,EAAE,EAAE;wBAAEoC,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,GAAG,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,gBACzDvE,OAAA,CAACR,MAAM;wBACL2E,EAAE,EAAE;0BACFuC,OAAO,EAAEjD,cAAc,CAACZ,KAAK,CAAC5B,IAAI,CAAC;0BACnCuE,KAAK,EAAE,EAAE;0BACTmB,MAAM,EAAE,EAAE;0BACVC,QAAQ,EAAE;wBACZ,CAAE;wBAAArC,QAAA,EAEDpB,WAAW,CAACN,KAAK,CAAC5B,IAAI;sBAAC;wBAAA4D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC,eACThF,OAAA,CAACvB,UAAU;wBAACoI,OAAO,EAAC,OAAO;wBAAC1C,EAAE,EAAE;0BAAEyB,UAAU,EAAE;wBAAI,CAAE;wBAAArB,QAAA,EACjD1B,KAAK,CAAC5B;sBAAI;wBAAA4D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZhF,OAAA,CAACjB,SAAS;oBAAAwF,QAAA,eACRvE,OAAA,CAACvB,UAAU;sBAACoI,OAAO,EAAC,OAAO;sBAAClC,KAAK,EAAC,SAAS;sBAACR,EAAE,EAAE;wBAAEyB,UAAU,EAAE;sBAAI,CAAE;sBAAArB,QAAA,EACjE1B,KAAK,CAAC3B;oBAAK;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZhF,OAAA,CAACjB,SAAS;oBAAAwF,QAAA,eACRvE,OAAA,CAACvB,UAAU;sBAACoI,OAAO,EAAC,OAAO;sBAAAtC,QAAA,EACxB1B,KAAK,CAAC1B;oBAAK;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZhF,OAAA,CAACjB,SAAS;oBAAAwF,QAAA,eACRvE,OAAA,CAACX,IAAI;sBACHyH,KAAK,EAAEjE,KAAK,CAACxB,UAAW;sBACxBsD,KAAK,EAAE9B,KAAK,CAACxB,UAAU,KAAK,KAAK,GAAG,SAAS,GAAG,OAAQ;sBACxD0F,IAAI,EAAC,OAAO;sBACZ5C,EAAE,EAAE;wBAAEsB,QAAQ,EAAE;sBAAG;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZhF,OAAA,CAACjB,SAAS;oBAAAwF,QAAA,eACRvE,OAAA,CAACX,IAAI;sBACHyH,KAAK,EAAEjE,KAAK,CAACtB,WAAY;sBACzBoD,KAAK,EAAEd,cAAc,CAAChB,KAAK,CAACtB,WAAW,CAAE;sBACzCwF,IAAI,EAAC,OAAO;sBACZ5C,EAAE,EAAE;wBAAEsB,QAAQ,EAAE;sBAAG;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZhF,OAAA,CAACjB,SAAS;oBAAAwF,QAAA,eACRvE,OAAA,CAACvB,UAAU;sBAACoI,OAAO,EAAC,OAAO;sBAAAtC,QAAA,EACxB1B,KAAK,CAACzB;oBAAQ;sBAAAyD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZhF,OAAA,CAACjB,SAAS;oBAAAwF,QAAA,eACRvE,OAAA,CAACzB,GAAG;sBAAC4F,EAAE,EAAE;wBAAEoC,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,GAAG,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,gBACzDvE,OAAA,CAACX,IAAI;wBACHyH,KAAK,EAAEjE,KAAK,CAACvB,MAAO;wBACpBqD,KAAK,EAAEd,cAAc,CAAChB,KAAK,CAACvB,MAAM,CAAE;wBACpCyF,IAAI,EAAC,OAAO;wBACZ5C,EAAE,EAAE;0BAAEsB,QAAQ,EAAE;wBAAG;sBAAE;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eACFhF,OAAA,CAACV,UAAU;wBACTyH,IAAI,EAAC,OAAO;wBACZpC,KAAK,EAAC,SAAS;wBACfqB,OAAO,EAAEA,CAAA,KAAMpD,eAAe,CAACC,KAAK,CAAE;wBAAA0B,QAAA,eAEtCvE,OAAA,CAACH,QAAQ;0BAAC+G,QAAQ,EAAC;wBAAO;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GA7EPnC,KAAK,CAAC7B,EAAE;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8EL,CAAC;cAEf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGjBhF,OAAA,CAACzB,GAAG;UAAC4F,EAAE,EAAE;YAAEoC,OAAO,EAAE,MAAM;YAAES,cAAc,EAAE,eAAe;YAAER,UAAU,EAAE,QAAQ;YAAES,CAAC,EAAE;UAAE,CAAE;UAAA1C,QAAA,gBACxFvE,OAAA,CAACvB,UAAU;YAACoI,OAAO,EAAC,OAAO;YAAClC,KAAK,EAAC,gBAAgB;YAAAJ,QAAA,GAAC,UACzC,EAACpE,IAAI,GAAGE,WAAW,GAAG,CAAC,EAAC,MAAI,EAAC6G,IAAI,CAACC,GAAG,CAAC,CAAChH,IAAI,GAAG,CAAC,IAAIE,WAAW,EAAE0D,cAAc,CAACrB,MAAM,CAAC,EAAC,MAAI,EAACqB,cAAc,CAACrB,MAAM;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC,eACbhF,OAAA,CAACT,eAAe;YACd6H,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAE;YAChCf,SAAS,EAAC,KAAK;YACfgB,KAAK,EAAEtD,cAAc,CAACrB,MAAO;YAC7BrC,WAAW,EAAEA,WAAY;YACzBF,IAAI,EAAEA,IAAK;YACXmH,YAAY,EAAE9F,gBAAiB;YAC/B+F,mBAAmB,EAAE5F,uBAAwB;YAC7CwC,EAAE,EAAE;cACF,+BAA+B,EAAE;gBAC/BE,SAAS,EAAE;cACb,CAAC;cACD,wEAAwE,EAAE;gBACxEmD,MAAM,EAAE;cACV;YACF;UAAE;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAC9E,EAAA,CA5YID,SAAS;AAAAwH,EAAA,GAATxH,SAAS;AA8Yf,eAAeA,SAAS;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}