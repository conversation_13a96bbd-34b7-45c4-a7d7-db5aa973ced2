{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Dash\\\\src\\\\components\\\\AllAgents.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Container, Typography, Breadcrumbs, Link, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, InputAdornment, Chip, IconButton, TablePagination, Avatar, Checkbox } from '@mui/material';\nimport { Search as SearchIcon, Edit as EditIcon } from '@mui/icons-material';\nimport EditAgentModal from './EditAgentModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllAgents = () => {\n  _s();\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selected, setSelected] = useState([]);\n  const [editModalOpen, setEditModalOpen] = useState(false);\n  const [selectedAgent, setSelectedAgent] = useState(null);\n\n  // Sample agent data\n  const agentsData = [{\n    id: 1,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 2,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 3,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 4,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 5,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 6,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 7,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 8,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 9,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'No',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 10,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'No',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }];\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n  const handleChangeRowsPerPage = event => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n  const handleSearchChange = event => {\n    setSearchTerm(event.target.value);\n  };\n  const handleSelectAllClick = event => {\n    if (event.target.checked) {\n      const newSelected = agentsData.map(n => n.id);\n      setSelected(newSelected);\n      return;\n    }\n    setSelected([]);\n  };\n  const handleClick = (event, id) => {\n    const selectedIndex = selected.indexOf(id);\n    let newSelected = [];\n    if (selectedIndex === -1) {\n      newSelected = newSelected.concat(selected, id);\n    } else if (selectedIndex === 0) {\n      newSelected = newSelected.concat(selected.slice(1));\n    } else if (selectedIndex === selected.length - 1) {\n      newSelected = newSelected.concat(selected.slice(0, -1));\n    } else if (selectedIndex > 0) {\n      newSelected = newSelected.concat(selected.slice(0, selectedIndex), selected.slice(selectedIndex + 1));\n    }\n    setSelected(newSelected);\n  };\n  const isSelected = id => selected.indexOf(id) !== -1;\n  const getInitials = name => {\n    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);\n  };\n  const getAvatarColor = name => {\n    const colors = ['#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4caf50', '#8bc34a', '#cddc39'];\n    const index = name.charCodeAt(0) % colors.length;\n    return colors[index];\n  };\n  const getStatusColor = status => {\n    switch (status.toLowerCase()) {\n      case 'verified':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'rejected':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const filteredAgents = agentsData.filter(agent => agent.name.toLowerCase().includes(searchTerm.toLowerCase()) || agent.email.toLowerCase().includes(searchTerm.toLowerCase()) || agent.phone.includes(searchTerm));\n  const paginatedAgents = filteredAgents.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      backgroundColor: '#f5f5f5',\n      minHeight: '100vh',\n      py: 3\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: [/*#__PURE__*/_jsxDEV(Breadcrumbs, {\n        \"aria-label\": \"breadcrumb\",\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          underline: \"hover\",\n          color: \"inherit\",\n          href: \"/\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"primary\",\n          children: \"All Agents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Search by username, email\",\n          value: searchTerm,\n          onChange: handleSearchChange,\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                color: \"action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)\n          },\n          sx: {\n            maxWidth: 400,\n            backgroundColor: 'white',\n            '& .MuiOutlinedInput-root': {\n              borderRadius: 2\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          width: '100%',\n          mb: 2,\n          borderRadius: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            sx: {\n              minWidth: 750\n            },\n            \"aria-labelledby\": \"tableTitle\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  backgroundColor: '#f8f9fa'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  padding: \"checkbox\",\n                  children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                    color: \"primary\",\n                    indeterminate: selected.length > 0 && selected.length < agentsData.length,\n                    checked: agentsData.length > 0 && selected.length === agentsData.length,\n                    onChange: handleSelectAllClick\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Phone Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Commission\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Running Lead\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Joining Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: paginatedAgents.map((agent, index) => {\n                const isItemSelected = isSelected(agent.id);\n                const labelId = `enhanced-table-checkbox-${index}`;\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  hover: true,\n                  onClick: event => handleClick(event, agent.id),\n                  role: \"checkbox\",\n                  \"aria-checked\": isItemSelected,\n                  tabIndex: -1,\n                  selected: isItemSelected,\n                  sx: {\n                    cursor: 'pointer'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    padding: \"checkbox\",\n                    children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                      color: \"primary\",\n                      checked: isItemSelected,\n                      inputProps: {\n                        'aria-labelledby': labelId\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    component: \"th\",\n                    id: labelId,\n                    scope: \"row\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                        sx: {\n                          bgcolor: getAvatarColor(agent.name),\n                          width: 32,\n                          height: 32,\n                          fontSize: '0.875rem'\n                        },\n                        children: getInitials(agent.name)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 312,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 500\n                        },\n                        children: agent.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 322,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"primary\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: agent.phone\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 328,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: agent.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: agent.commission,\n                      color: agent.commission === 'Yes' ? 'success' : 'error',\n                      size: \"small\",\n                      sx: {\n                        minWidth: 60\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: agent.runningLead,\n                      color: getStatusColor(agent.runningLead),\n                      size: \"small\",\n                      sx: {\n                        minWidth: 80\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 346,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: agent.joinDate\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Chip, {\n                        label: agent.status,\n                        color: getStatusColor(agent.status),\n                        size: \"small\",\n                        sx: {\n                          minWidth: 80\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 360,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"primary\",\n                        children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 367,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 366,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this)]\n                }, agent.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"Showing \", page * rowsPerPage + 1, \" to \", Math.min((page + 1) * rowsPerPage, filteredAgents.length), \" of \", filteredAgents.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n            rowsPerPageOptions: [5, 10, 25],\n            component: \"div\",\n            count: filteredAgents.length,\n            rowsPerPage: rowsPerPage,\n            page: page,\n            onPageChange: handleChangePage,\n            onRowsPerPageChange: handleChangeRowsPerPage,\n            sx: {\n              '& .MuiTablePagination-toolbar': {\n                minHeight: 'auto'\n              },\n              '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {\n                margin: 0\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 228,\n    columnNumber: 5\n  }, this);\n};\n_s(AllAgents, \"Jybuvw6V2h6Q2rZWORTqnvKxXFI=\");\n_c = AllAgents;\nexport default AllAgents;\nvar _c;\n$RefreshReg$(_c, \"AllAgents\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Container", "Typography", "Breadcrumbs", "Link", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TextField", "InputAdornment", "Chip", "IconButton", "TablePagination", "Avatar", "Checkbox", "Search", "SearchIcon", "Edit", "EditIcon", "EditAgentModal", "jsxDEV", "_jsxDEV", "AllAgents", "_s", "page", "setPage", "rowsPerPage", "setRowsPerPage", "searchTerm", "setSearchTerm", "selected", "setSelected", "editModalOpen", "setEditModalOpen", "selectedAgent", "setSelectedAgent", "agentsData", "id", "name", "phone", "email", "joinDate", "commission", "status", "runningLead", "handleChangePage", "event", "newPage", "handleChangeRowsPerPage", "parseInt", "target", "value", "handleSearchChange", "handleSelectAllClick", "checked", "newSelected", "map", "n", "handleClick", "selectedIndex", "indexOf", "concat", "slice", "length", "isSelected", "getInitials", "split", "word", "char<PERSON>t", "join", "toUpperCase", "getAvatarColor", "colors", "index", "charCodeAt", "getStatusColor", "toLowerCase", "filteredAgents", "filter", "agent", "includes", "paginatedAgents", "sx", "backgroundColor", "minHeight", "py", "children", "max<PERSON><PERSON><PERSON>", "mb", "underline", "color", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "placeholder", "onChange", "InputProps", "startAdornment", "position", "borderRadius", "width", "min<PERSON><PERSON><PERSON>", "padding", "indeterminate", "fontWeight", "isItemSelected", "labelId", "hover", "onClick", "role", "tabIndex", "cursor", "inputProps", "component", "scope", "display", "alignItems", "gap", "bgcolor", "height", "fontSize", "variant", "label", "size", "justifyContent", "p", "Math", "min", "rowsPerPageOptions", "count", "onPageChange", "onRowsPerPageChange", "margin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Dash/src/components/AllAgents.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Breadcrumbs,\n  Link,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TextField,\n  InputAdornment,\n  Chip,\n  IconButton,\n  TablePagination,\n  Avatar,\n  Checkbox,\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Edit as EditIcon,\n} from '@mui/icons-material';\nimport EditAgentModal from './EditAgentModal';\n\nconst AllAgents = () => {\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selected, setSelected] = useState([]);\n  const [editModalOpen, setEditModalOpen] = useState(false);\n  const [selectedAgent, setSelectedAgent] = useState(null);\n\n  // Sample agent data\n  const agentsData = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 2,\n      name: '<PERSON>',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 3,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 4,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 5,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 6,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 7,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 8,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 9,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'No',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 10,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'No',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n  ];\n\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n\n  const handleChangeRowsPerPage = (event) => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n\n  const handleSearchChange = (event) => {\n    setSearchTerm(event.target.value);\n  };\n\n  const handleSelectAllClick = (event) => {\n    if (event.target.checked) {\n      const newSelected = agentsData.map((n) => n.id);\n      setSelected(newSelected);\n      return;\n    }\n    setSelected([]);\n  };\n\n  const handleClick = (event, id) => {\n    const selectedIndex = selected.indexOf(id);\n    let newSelected = [];\n\n    if (selectedIndex === -1) {\n      newSelected = newSelected.concat(selected, id);\n    } else if (selectedIndex === 0) {\n      newSelected = newSelected.concat(selected.slice(1));\n    } else if (selectedIndex === selected.length - 1) {\n      newSelected = newSelected.concat(selected.slice(0, -1));\n    } else if (selectedIndex > 0) {\n      newSelected = newSelected.concat(\n        selected.slice(0, selectedIndex),\n        selected.slice(selectedIndex + 1),\n      );\n    }\n    setSelected(newSelected);\n  };\n\n  const isSelected = (id) => selected.indexOf(id) !== -1;\n\n  const getInitials = (name) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  const getAvatarColor = (name) => {\n    const colors = [\n      '#f44336', '#e91e63', '#9c27b0', '#673ab7',\n      '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4',\n      '#009688', '#4caf50', '#8bc34a', '#cddc39',\n    ];\n    const index = name.charCodeAt(0) % colors.length;\n    return colors[index];\n  };\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'verified':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'rejected':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const filteredAgents = agentsData.filter(agent =>\n    agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    agent.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    agent.phone.includes(searchTerm)\n  );\n\n  const paginatedAgents = filteredAgents.slice(\n    page * rowsPerPage,\n    page * rowsPerPage + rowsPerPage\n  );\n\n  return (\n    <Box sx={{ backgroundColor: '#f5f5f5', minHeight: '100vh', py: 3 }}>\n      <Container maxWidth=\"xl\">\n        {/* Breadcrumbs */}\n        <Breadcrumbs aria-label=\"breadcrumb\" sx={{ mb: 3 }}>\n          <Link underline=\"hover\" color=\"inherit\" href=\"/\">\n            Dashboard\n          </Link>\n          <Typography color=\"primary\">All Agents</Typography>\n        </Breadcrumbs>\n\n        {/* Search Bar */}\n        <Box sx={{ mb: 3 }}>\n          <TextField\n            fullWidth\n            placeholder=\"Search by username, email\"\n            value={searchTerm}\n            onChange={handleSearchChange}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon color=\"action\" />\n                </InputAdornment>\n              ),\n            }}\n            sx={{\n              maxWidth: 400,\n              backgroundColor: 'white',\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 2,\n              },\n            }}\n          />\n        </Box>\n\n        {/* Table */}\n        <Paper sx={{ width: '100%', mb: 2, borderRadius: 2 }}>\n          <TableContainer>\n            <Table sx={{ minWidth: 750 }} aria-labelledby=\"tableTitle\">\n              <TableHead>\n                <TableRow sx={{ backgroundColor: '#f8f9fa' }}>\n                  <TableCell padding=\"checkbox\">\n                    <Checkbox\n                      color=\"primary\"\n                      indeterminate={selected.length > 0 && selected.length < agentsData.length}\n                      checked={agentsData.length > 0 && selected.length === agentsData.length}\n                      onChange={handleSelectAllClick}\n                    />\n                  </TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Name</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Phone Number</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Email</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Commission</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Running Lead</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Joining Date</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Status</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {paginatedAgents.map((agent, index) => {\n                  const isItemSelected = isSelected(agent.id);\n                  const labelId = `enhanced-table-checkbox-${index}`;\n\n                  return (\n                    <TableRow\n                      hover\n                      onClick={(event) => handleClick(event, agent.id)}\n                      role=\"checkbox\"\n                      aria-checked={isItemSelected}\n                      tabIndex={-1}\n                      key={agent.id}\n                      selected={isItemSelected}\n                      sx={{ cursor: 'pointer' }}\n                    >\n                      <TableCell padding=\"checkbox\">\n                        <Checkbox\n                          color=\"primary\"\n                          checked={isItemSelected}\n                          inputProps={{\n                            'aria-labelledby': labelId,\n                          }}\n                        />\n                      </TableCell>\n                      <TableCell component=\"th\" id={labelId} scope=\"row\">\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                          <Avatar\n                            sx={{\n                              bgcolor: getAvatarColor(agent.name),\n                              width: 32,\n                              height: 32,\n                              fontSize: '0.875rem',\n                            }}\n                          >\n                            {getInitials(agent.name)}\n                          </Avatar>\n                          <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                            {agent.name}\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\" color=\"primary\" sx={{ fontWeight: 500 }}>\n                          {agent.phone}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {agent.email}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={agent.commission}\n                          color={agent.commission === 'Yes' ? 'success' : 'error'}\n                          size=\"small\"\n                          sx={{ minWidth: 60 }}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={agent.runningLead}\n                          color={getStatusColor(agent.runningLead)}\n                          size=\"small\"\n                          sx={{ minWidth: 80 }}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {agent.joinDate}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          <Chip\n                            label={agent.status}\n                            color={getStatusColor(agent.status)}\n                            size=\"small\"\n                            sx={{ minWidth: 80 }}\n                          />\n                          <IconButton size=\"small\" color=\"primary\">\n                            <EditIcon fontSize=\"small\" />\n                          </IconButton>\n                        </Box>\n                      </TableCell>\n                    </TableRow>\n                  );\n                })}\n              </TableBody>\n            </Table>\n          </TableContainer>\n          \n          {/* Pagination */}\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Showing {page * rowsPerPage + 1} to {Math.min((page + 1) * rowsPerPage, filteredAgents.length)} of {filteredAgents.length}\n            </Typography>\n            <TablePagination\n              rowsPerPageOptions={[5, 10, 25]}\n              component=\"div\"\n              count={filteredAgents.length}\n              rowsPerPage={rowsPerPage}\n              page={page}\n              onPageChange={handleChangePage}\n              onRowsPerPageChange={handleChangeRowsPerPage}\n              sx={{\n                '& .MuiTablePagination-toolbar': {\n                  minHeight: 'auto',\n                },\n                '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {\n                  margin: 0,\n                },\n              }}\n            />\n          </Box>\n        </Paper>\n      </Container>\n    </Box>\n  );\n};\n\nexport default AllAgents;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,eAAe,EACfC,MAAM,EACNC,QAAQ,QACH,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAMyC,UAAU,GAAG,CACjB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,EAAE;IACNC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC3CtB,OAAO,CAACsB,OAAO,CAAC;EAClB,CAAC;EAED,MAAMC,uBAAuB,GAAIF,KAAK,IAAK;IACzCnB,cAAc,CAACsB,QAAQ,CAACH,KAAK,CAACI,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,CAAC;IAChD1B,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC;EAED,MAAM2B,kBAAkB,GAAIN,KAAK,IAAK;IACpCjB,aAAa,CAACiB,KAAK,CAACI,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;EAED,MAAME,oBAAoB,GAAIP,KAAK,IAAK;IACtC,IAAIA,KAAK,CAACI,MAAM,CAACI,OAAO,EAAE;MACxB,MAAMC,WAAW,GAAGnB,UAAU,CAACoB,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACpB,EAAE,CAAC;MAC/CN,WAAW,CAACwB,WAAW,CAAC;MACxB;IACF;IACAxB,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EAED,MAAM2B,WAAW,GAAGA,CAACZ,KAAK,EAAET,EAAE,KAAK;IACjC,MAAMsB,aAAa,GAAG7B,QAAQ,CAAC8B,OAAO,CAACvB,EAAE,CAAC;IAC1C,IAAIkB,WAAW,GAAG,EAAE;IAEpB,IAAII,aAAa,KAAK,CAAC,CAAC,EAAE;MACxBJ,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAC/B,QAAQ,EAAEO,EAAE,CAAC;IAChD,CAAC,MAAM,IAAIsB,aAAa,KAAK,CAAC,EAAE;MAC9BJ,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAC/B,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM,IAAIH,aAAa,KAAK7B,QAAQ,CAACiC,MAAM,GAAG,CAAC,EAAE;MAChDR,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAC/B,QAAQ,CAACgC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC,MAAM,IAAIH,aAAa,GAAG,CAAC,EAAE;MAC5BJ,WAAW,GAAGA,WAAW,CAACM,MAAM,CAC9B/B,QAAQ,CAACgC,KAAK,CAAC,CAAC,EAAEH,aAAa,CAAC,EAChC7B,QAAQ,CAACgC,KAAK,CAACH,aAAa,GAAG,CAAC,CAClC,CAAC;IACH;IACA5B,WAAW,CAACwB,WAAW,CAAC;EAC1B,CAAC;EAED,MAAMS,UAAU,GAAI3B,EAAE,IAAKP,QAAQ,CAAC8B,OAAO,CAACvB,EAAE,CAAC,KAAK,CAAC,CAAC;EAEtD,MAAM4B,WAAW,GAAI3B,IAAI,IAAK;IAC5B,OAAOA,IAAI,CACR4B,KAAK,CAAC,GAAG,CAAC,CACVV,GAAG,CAACW,IAAI,IAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC3BC,IAAI,CAAC,EAAE,CAAC,CACRC,WAAW,CAAC,CAAC,CACbR,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EAED,MAAMS,cAAc,GAAIjC,IAAI,IAAK;IAC/B,MAAMkC,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC3C;IACD,MAAMC,KAAK,GAAGnC,IAAI,CAACoC,UAAU,CAAC,CAAC,CAAC,GAAGF,MAAM,CAACT,MAAM;IAChD,OAAOS,MAAM,CAACC,KAAK,CAAC;EACtB,CAAC;EAED,MAAME,cAAc,GAAIhC,MAAM,IAAK;IACjC,QAAQA,MAAM,CAACiC,WAAW,CAAC,CAAC;MAC1B,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMC,cAAc,GAAGzC,UAAU,CAAC0C,MAAM,CAACC,KAAK,IAC5CA,KAAK,CAACzC,IAAI,CAACsC,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACpD,UAAU,CAACgD,WAAW,CAAC,CAAC,CAAC,IAC3DG,KAAK,CAACvC,KAAK,CAACoC,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACpD,UAAU,CAACgD,WAAW,CAAC,CAAC,CAAC,IAC5DG,KAAK,CAACxC,KAAK,CAACyC,QAAQ,CAACpD,UAAU,CACjC,CAAC;EAED,MAAMqD,eAAe,GAAGJ,cAAc,CAACf,KAAK,CAC1CtC,IAAI,GAAGE,WAAW,EAClBF,IAAI,GAAGE,WAAW,GAAGA,WACvB,CAAC;EAED,oBACEL,OAAA,CAACzB,GAAG;IAACsF,EAAE,EAAE;MAAEC,eAAe,EAAE,SAAS;MAAEC,SAAS,EAAE,OAAO;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eACjEjE,OAAA,CAACxB,SAAS;MAAC0F,QAAQ,EAAC,IAAI;MAAAD,QAAA,gBAEtBjE,OAAA,CAACtB,WAAW;QAAC,cAAW,YAAY;QAACmF,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,gBACjDjE,OAAA,CAACrB,IAAI;UAACyF,SAAS,EAAC,OAAO;UAACC,KAAK,EAAC,SAAS;UAACC,IAAI,EAAC,GAAG;UAAAL,QAAA,EAAC;QAEjD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP1E,OAAA,CAACvB,UAAU;UAAC4F,KAAK,EAAC,SAAS;UAAAJ,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAGd1E,OAAA,CAACzB,GAAG;QAACsF,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eACjBjE,OAAA,CAACb,SAAS;UACRwF,SAAS;UACTC,WAAW,EAAC,2BAA2B;UACvC9C,KAAK,EAAEvB,UAAW;UAClBsE,QAAQ,EAAE9C,kBAAmB;UAC7B+C,UAAU,EAAE;YACVC,cAAc,eACZ/E,OAAA,CAACZ,cAAc;cAAC4F,QAAQ,EAAC,OAAO;cAAAf,QAAA,eAC9BjE,OAAA,CAACL,UAAU;gBAAC0E,KAAK,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAEpB,CAAE;UACFb,EAAE,EAAE;YACFK,QAAQ,EAAE,GAAG;YACbJ,eAAe,EAAE,OAAO;YACxB,0BAA0B,EAAE;cAC1BmB,YAAY,EAAE;YAChB;UACF;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN1E,OAAA,CAACpB,KAAK;QAACiF,EAAE,EAAE;UAAEqB,KAAK,EAAE,MAAM;UAAEf,EAAE,EAAE,CAAC;UAAEc,YAAY,EAAE;QAAE,CAAE;QAAAhB,QAAA,gBACnDjE,OAAA,CAAChB,cAAc;UAAAiF,QAAA,eACbjE,OAAA,CAACnB,KAAK;YAACgF,EAAE,EAAE;cAAEsB,QAAQ,EAAE;YAAI,CAAE;YAAC,mBAAgB,YAAY;YAAAlB,QAAA,gBACxDjE,OAAA,CAACf,SAAS;cAAAgF,QAAA,eACRjE,OAAA,CAACd,QAAQ;gBAAC2E,EAAE,EAAE;kBAAEC,eAAe,EAAE;gBAAU,CAAE;gBAAAG,QAAA,gBAC3CjE,OAAA,CAACjB,SAAS;kBAACqG,OAAO,EAAC,UAAU;kBAAAnB,QAAA,eAC3BjE,OAAA,CAACP,QAAQ;oBACP4E,KAAK,EAAC,SAAS;oBACfgB,aAAa,EAAE5E,QAAQ,CAACiC,MAAM,GAAG,CAAC,IAAIjC,QAAQ,CAACiC,MAAM,GAAG3B,UAAU,CAAC2B,MAAO;oBAC1ET,OAAO,EAAElB,UAAU,CAAC2B,MAAM,GAAG,CAAC,IAAIjC,QAAQ,CAACiC,MAAM,KAAK3B,UAAU,CAAC2B,MAAO;oBACxEmC,QAAQ,EAAE7C;kBAAqB;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ1E,OAAA,CAACjB,SAAS;kBAAC8E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7E1E,OAAA,CAACjB,SAAS;kBAAC8E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrF1E,OAAA,CAACjB,SAAS;kBAAC8E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9E1E,OAAA,CAACjB,SAAS;kBAAC8E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnF1E,OAAA,CAACjB,SAAS;kBAAC8E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrF1E,OAAA,CAACjB,SAAS;kBAAC8E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrF1E,OAAA,CAACjB,SAAS;kBAAC8E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZ1E,OAAA,CAAClB,SAAS;cAAAmF,QAAA,EACPL,eAAe,CAACzB,GAAG,CAAC,CAACuB,KAAK,EAAEN,KAAK,KAAK;gBACrC,MAAMmC,cAAc,GAAG5C,UAAU,CAACe,KAAK,CAAC1C,EAAE,CAAC;gBAC3C,MAAMwE,OAAO,GAAG,2BAA2BpC,KAAK,EAAE;gBAElD,oBACEpD,OAAA,CAACd,QAAQ;kBACPuG,KAAK;kBACLC,OAAO,EAAGjE,KAAK,IAAKY,WAAW,CAACZ,KAAK,EAAEiC,KAAK,CAAC1C,EAAE,CAAE;kBACjD2E,IAAI,EAAC,UAAU;kBACf,gBAAcJ,cAAe;kBAC7BK,QAAQ,EAAE,CAAC,CAAE;kBAEbnF,QAAQ,EAAE8E,cAAe;kBACzB1B,EAAE,EAAE;oBAAEgC,MAAM,EAAE;kBAAU,CAAE;kBAAA5B,QAAA,gBAE1BjE,OAAA,CAACjB,SAAS;oBAACqG,OAAO,EAAC,UAAU;oBAAAnB,QAAA,eAC3BjE,OAAA,CAACP,QAAQ;sBACP4E,KAAK,EAAC,SAAS;sBACfpC,OAAO,EAAEsD,cAAe;sBACxBO,UAAU,EAAE;wBACV,iBAAiB,EAAEN;sBACrB;oBAAE;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZ1E,OAAA,CAACjB,SAAS;oBAACgH,SAAS,EAAC,IAAI;oBAAC/E,EAAE,EAAEwE,OAAQ;oBAACQ,KAAK,EAAC,KAAK;oBAAA/B,QAAA,eAChDjE,OAAA,CAACzB,GAAG;sBAACsF,EAAE,EAAE;wBAAEoC,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,GAAG,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,gBACzDjE,OAAA,CAACR,MAAM;wBACLqE,EAAE,EAAE;0BACFuC,OAAO,EAAElD,cAAc,CAACQ,KAAK,CAACzC,IAAI,CAAC;0BACnCiE,KAAK,EAAE,EAAE;0BACTmB,MAAM,EAAE,EAAE;0BACVC,QAAQ,EAAE;wBACZ,CAAE;wBAAArC,QAAA,EAEDrB,WAAW,CAACc,KAAK,CAACzC,IAAI;sBAAC;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC,eACT1E,OAAA,CAACvB,UAAU;wBAAC8H,OAAO,EAAC,OAAO;wBAAC1C,EAAE,EAAE;0BAAEyB,UAAU,EAAE;wBAAI,CAAE;wBAAArB,QAAA,EACjDP,KAAK,CAACzC;sBAAI;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ1E,OAAA,CAACjB,SAAS;oBAAAkF,QAAA,eACRjE,OAAA,CAACvB,UAAU;sBAAC8H,OAAO,EAAC,OAAO;sBAAClC,KAAK,EAAC,SAAS;sBAACR,EAAE,EAAE;wBAAEyB,UAAU,EAAE;sBAAI,CAAE;sBAAArB,QAAA,EACjEP,KAAK,CAACxC;oBAAK;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZ1E,OAAA,CAACjB,SAAS;oBAAAkF,QAAA,eACRjE,OAAA,CAACvB,UAAU;sBAAC8H,OAAO,EAAC,OAAO;sBAAAtC,QAAA,EACxBP,KAAK,CAACvC;oBAAK;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZ1E,OAAA,CAACjB,SAAS;oBAAAkF,QAAA,eACRjE,OAAA,CAACX,IAAI;sBACHmH,KAAK,EAAE9C,KAAK,CAACrC,UAAW;sBACxBgD,KAAK,EAAEX,KAAK,CAACrC,UAAU,KAAK,KAAK,GAAG,SAAS,GAAG,OAAQ;sBACxDoF,IAAI,EAAC,OAAO;sBACZ5C,EAAE,EAAE;wBAAEsB,QAAQ,EAAE;sBAAG;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZ1E,OAAA,CAACjB,SAAS;oBAAAkF,QAAA,eACRjE,OAAA,CAACX,IAAI;sBACHmH,KAAK,EAAE9C,KAAK,CAACnC,WAAY;sBACzB8C,KAAK,EAAEf,cAAc,CAACI,KAAK,CAACnC,WAAW,CAAE;sBACzCkF,IAAI,EAAC,OAAO;sBACZ5C,EAAE,EAAE;wBAAEsB,QAAQ,EAAE;sBAAG;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZ1E,OAAA,CAACjB,SAAS;oBAAAkF,QAAA,eACRjE,OAAA,CAACvB,UAAU;sBAAC8H,OAAO,EAAC,OAAO;sBAAAtC,QAAA,EACxBP,KAAK,CAACtC;oBAAQ;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZ1E,OAAA,CAACjB,SAAS;oBAAAkF,QAAA,eACRjE,OAAA,CAACzB,GAAG;sBAACsF,EAAE,EAAE;wBAAEoC,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,GAAG,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,gBACzDjE,OAAA,CAACX,IAAI;wBACHmH,KAAK,EAAE9C,KAAK,CAACpC,MAAO;wBACpB+C,KAAK,EAAEf,cAAc,CAACI,KAAK,CAACpC,MAAM,CAAE;wBACpCmF,IAAI,EAAC,OAAO;wBACZ5C,EAAE,EAAE;0BAAEsB,QAAQ,EAAE;wBAAG;sBAAE;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eACF1E,OAAA,CAACV,UAAU;wBAACmH,IAAI,EAAC,OAAO;wBAACpC,KAAK,EAAC,SAAS;wBAAAJ,QAAA,eACtCjE,OAAA,CAACH,QAAQ;0BAACyG,QAAQ,EAAC;wBAAO;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAzEPhB,KAAK,CAAC1C,EAAE;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0EL,CAAC;cAEf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGjB1E,OAAA,CAACzB,GAAG;UAACsF,EAAE,EAAE;YAAEoC,OAAO,EAAE,MAAM;YAAES,cAAc,EAAE,eAAe;YAAER,UAAU,EAAE,QAAQ;YAAES,CAAC,EAAE;UAAE,CAAE;UAAA1C,QAAA,gBACxFjE,OAAA,CAACvB,UAAU;YAAC8H,OAAO,EAAC,OAAO;YAAClC,KAAK,EAAC,gBAAgB;YAAAJ,QAAA,GAAC,UACzC,EAAC9D,IAAI,GAAGE,WAAW,GAAG,CAAC,EAAC,MAAI,EAACuG,IAAI,CAACC,GAAG,CAAC,CAAC1G,IAAI,GAAG,CAAC,IAAIE,WAAW,EAAEmD,cAAc,CAACd,MAAM,CAAC,EAAC,MAAI,EAACc,cAAc,CAACd,MAAM;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC,eACb1E,OAAA,CAACT,eAAe;YACduH,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAE;YAChCf,SAAS,EAAC,KAAK;YACfgB,KAAK,EAAEvD,cAAc,CAACd,MAAO;YAC7BrC,WAAW,EAAEA,WAAY;YACzBF,IAAI,EAAEA,IAAK;YACX6G,YAAY,EAAExF,gBAAiB;YAC/ByF,mBAAmB,EAAEtF,uBAAwB;YAC7CkC,EAAE,EAAE;cACF,+BAA+B,EAAE;gBAC/BE,SAAS,EAAE;cACb,CAAC;cACD,wEAAwE,EAAE;gBACxEmD,MAAM,EAAE;cACV;YACF;UAAE;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACxE,EAAA,CAxXID,SAAS;AAAAkH,EAAA,GAATlH,SAAS;AA0Xf,eAAeA,SAAS;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}