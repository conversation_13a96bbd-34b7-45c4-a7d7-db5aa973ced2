{"version": 3, "file": "react-router.development.js", "sources": ["../../lib/context.ts", "../../lib/hooks.tsx", "../../lib/deprecations.ts", "../../lib/components.tsx", "../../index.ts"], "sourcesContent": ["import * as React from \"react\";\nimport type {\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  AgnosticRouteMatch,\n  History,\n  LazyRouteFunction,\n  Location,\n  Action as NavigationType,\n  RelativeRoutingType,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject\n  // Omit `future` since those can be pulled from the `router`\n  // `NavigationContext` needs future since it doesn't have a `router` in all cases\n  extends Omit<NavigationContextObject, \"future\"> {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  flushSync?: boolean;\n  viewTransition?: boolean;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n  future: {\n    v7_relativeSplatPath: boolean;\n  };\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n  isDataRoute: boolean;\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n  isDataRoute: false,\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  Blocker,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RevalidationState,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  IDLE_BLOCKER,\n  Action as NavigationType,\n  UNSAFE_convertRouteMatchToUiMatch as convertRouteMatchToUiMatch,\n  UNSAFE_decodePath as decodePath,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  DataRouteMatch,\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/v6/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/v6/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, decodePath(pathname)),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\nconst navigateEffectWarning =\n  `You should call navigate() in a React.useEffect(), not when ` +\n  `your component is first rendered.`;\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(\n  cb: Parameters<typeof React.useLayoutEffect>[0]\n) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  let { isDataRoute } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\n\nfunction useNavigateUnstable(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let { basename, future, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our history listener yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history (but only if we're not in a data router,\n      // otherwise it'll prepend the basename inside of the router).\n      // If this is a root navigation, then we navigate to the raw basename\n      // which allows the basename to have full control over the presence of a\n      // trailing slash on root links\n      if (dataRouterContext == null && basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [\n      basename,\n      navigator,\n      routePathnamesJson,\n      locationPathname,\n      dataRouterContext,\n    ]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/v6/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/v6/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/v6/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/v6/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { future } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/v6/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nexport function useRoutesImpl(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string,\n  dataRouterState?: RemixRouter[\"state\"],\n  future?: RemixRouter[\"future\"]\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator } = React.useContext(NavigationContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined ||\n        matches[matches.length - 1].route.Component !== undefined ||\n        matches[matches.length - 1].route.lazy !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" ` +\n        `does not have an element or Component. This means it will render an <Outlet /> with a ` +\n        `null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterState,\n    future\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    console.error(\n      \"Error handled by React Router default ErrorBoundary:\",\n      error\n    );\n\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own <code style={codeStyles}>ErrorBoundary</code> or{\" \"}\n          <code style={codeStyles}>errorElement</code> prop on your route.\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\nconst defaultErrorElement = <DefaultErrorComponent />;\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (\n      state.location !== props.location ||\n      (state.revalidation !== \"idle\" && props.revalidation === \"idle\")\n    ) {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error !== undefined ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    (match.route.errorElement || match.route.ErrorBoundary)\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState: RemixRouter[\"state\"] | null = null,\n  future: RemixRouter[\"future\"] | null = null\n): React.ReactElement | null {\n  if (matches == null) {\n    if (!dataRouterState) {\n      return null;\n    }\n\n    if (dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else if (\n      future?.v7_partialHydration &&\n      parentMatches.length === 0 &&\n      !dataRouterState.initialized &&\n      dataRouterState.matches.length > 0\n    ) {\n      // Don't bail if we're initializing with partial hydration and we have\n      // router matches.  That means we're actively running `patchRoutesOnNavigation`\n      // so we should render down the partial matches to the appropriate\n      // `HydrateFallback`.  We only do this if `parentMatches` is empty so it\n      // only impacts the root matches for `RouterProvider` and no descendant\n      // `<Routes>`\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id] !== undefined\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for errors on route IDs: ${Object.keys(\n        errors\n      ).join(\",\")}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n\n      if (match.route.id) {\n        let { loaderData, errors } = dataRouterState;\n        let needsToRunLoader =\n          match.route.loader &&\n          loaderData[match.route.id] === undefined &&\n          (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error: any;\n    let shouldRenderHydrateFallback = false;\n    let errorElement: React.ReactNode | null = null;\n    let hydrateFallbackElement: React.ReactNode | null = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\n            \"route-fallback\",\n            false,\n            \"No `HydrateFallback` element provided to render during initial hydration\"\n          );\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children: React.ReactNode;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = <match.route.Component />;\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return (\n        <RenderedRoute\n          match={match}\n          routeContext={{\n            outlet,\n            matches,\n            isDataRoute: dataRouterState != null,\n          }}\n          children={children}\n        />\n      );\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState &&\n      (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        revalidation={dataRouterState.revalidation}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches, isDataRoute: true }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n}\n\nenum DataRouterStateHook {\n  UseBlocker = \"useBlocker\",\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n  UseRouteId = \"useRouteId\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nexport function useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(\n    () => ({\n      revalidate: dataRouterContext.router.revalidate,\n      state: state.revalidation,\n    }),\n    [dataRouterContext.router.revalidate, state.revalidation]\n  );\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches(): UIMatch[] {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () => matches.map((m) => convertRouteMatchToUiMatch(m, loaderData)),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router, basename } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (arg) => {\n      if (typeof shouldBlock !== \"function\") {\n        return !!shouldBlock;\n      }\n      if (basename === \"/\") {\n        return shouldBlock(arg);\n      }\n\n      // If they provided us a function and we've got an active basename, strip\n      // it from the locations we expose to the user to match the behavior of\n      // useLocation\n      let { currentLocation, nextLocation, historyAction } = arg;\n      return shouldBlock({\n        currentLocation: {\n          ...currentLocation,\n          pathname:\n            stripBasename(currentLocation.pathname, basename) ||\n            currentLocation.pathname,\n        },\n        nextLocation: {\n          ...nextLocation,\n          pathname:\n            stripBasename(nextLocation.pathname, basename) ||\n            nextLocation.pathname,\n        },\n        historyAction,\n      });\n    },\n    [basename, shouldBlock]\n  );\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey)\n    ? state.blockers.get(blockerKey)!\n    : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable(): NavigateFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our router subscriber yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        router.navigate(to);\n      } else {\n        router.navigate(to, { fromRouteId: id, ...options });\n      }\n    },\n    [router, id]\n  );\n\n  return navigate;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import type { FutureConfig as RouterFutureConfig } from \"@remix-run/router\";\nimport type { FutureConfig as RenderFutureConfig } from \"./components\";\n\nconst alreadyWarned: { [key: string]: boolean } = {};\n\nexport function warnOnce(key: string, message: string): void {\n  if (__DEV__ && !alreadyWarned[message]) {\n    alreadyWarned[message] = true;\n    console.warn(message);\n  }\n}\n\nconst logDeprecation = (flag: string, msg: string, link: string) =>\n  warnOnce(\n    flag,\n    `⚠️ React Router Future Flag Warning: ${msg}. ` +\n      `You can use the \\`${flag}\\` future flag to opt-in early. ` +\n      `For more information, see ${link}.`\n  );\n\nexport function logV6DeprecationWarnings(\n  renderFuture: Partial<RenderFutureConfig> | undefined,\n  routerFuture?: Omit<RouterFutureConfig, \"v7_prependBasename\">\n) {\n  if (renderFuture?.v7_startTransition === undefined) {\n    logDeprecation(\n      \"v7_startTransition\",\n      \"React Router will begin wrapping state updates in `React.startTransition` in v7\",\n      \"https://reactrouter.com/v6/upgrading/future#v7_starttransition\"\n    );\n  }\n\n  if (\n    renderFuture?.v7_relativeSplatPath === undefined &&\n    (!routerFuture || routerFuture.v7_relativeSplatPath === undefined)\n  ) {\n    logDeprecation(\n      \"v7_relativeSplatPath\",\n      \"Relative route resolution within Splat routes is changing in v7\",\n      \"https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath\"\n    );\n  }\n\n  if (routerFuture) {\n    if (routerFuture.v7_fetcherPersist === undefined) {\n      logDeprecation(\n        \"v7_fetcherPersist\",\n        \"The persistence behavior of fetchers is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist\"\n      );\n    }\n\n    if (routerFuture.v7_normalizeFormMethod === undefined) {\n      logDeprecation(\n        \"v7_normalizeFormMethod\",\n        \"Casing of `formMethod` fields is being normalized to uppercase in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod\"\n      );\n    }\n\n    if (routerFuture.v7_partialHydration === undefined) {\n      logDeprecation(\n        \"v7_partialHydration\",\n        \"`RouterProvider` hydration behavior is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_partialhydration\"\n      );\n    }\n\n    if (routerFuture.v7_skipActionErrorRevalidation === undefined) {\n      logDeprecation(\n        \"v7_skipActionErrorRevalidation\",\n        \"The revalidation behavior after 4xx/5xx `action` responses is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation\"\n      );\n    }\n  }\n}\n", "import type {\n  InitialEntry,\n  LazyRouteFunction,\n  Location,\n  MemoryHistory,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RouterState,\n  RouterSubscriber,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\nimport * as React from \"react\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./context\";\nimport {\n  _renderMatches,\n  useAsyncValue,\n  useInRouterContext,\n  useLocation,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  useRoutesImpl,\n} from \"./hooks\";\nimport { logV6DeprecationWarnings } from \"./deprecations\";\n\nexport interface FutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_startTransition: boolean;\n}\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n  // Only accept future flags relevant to rendering behavior\n  // routing flags should be accessed via router.future\n  future?: Partial<Pick<FutureConfig, \"v7_startTransition\">>;\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let { v7_startTransition } = future || {};\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (newState: RouterState) => {\n      if (v7_startTransition && startTransitionImpl) {\n        startTransitionImpl(() => setStateImpl(newState));\n      } else {\n        setStateImpl(newState);\n      }\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [router, future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={basename}\n            location={state.location}\n            navigationType={state.historyAction}\n            navigator={navigator}\n            future={{\n              v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n            }}\n          >\n            {state.initialized || router.future.v7_partialHydration ? (\n              <DataRoutes\n                routes={router.routes}\n                future={router.future}\n                state={state}\n              />\n            ) : (\n              fallbackElement\n            )}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  future?: Partial<FutureConfig>;\n}\n\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/v6/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n  future,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/v6/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  let { future, static: isStatic } = React.useContext(NavigationContext);\n\n  warning(\n    !isStatic,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(\n    to,\n    getResolveToMatches(matches, future.v7_relativeSplatPath),\n    locationPathname,\n    relative === \"path\"\n  );\n  let jsonPath = JSON.stringify(path);\n\n  React.useEffect(\n    () => navigate(JSON.parse(jsonPath), { replace, state, relative }),\n    [navigate, jsonPath, relative, replace, state]\n  );\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/v6/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<NonIndexRouteObject>;\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<IndexRouteObject>;\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/v6/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n  future?: Partial<Pick<FutureConfig, \"v7_relativeSplatPath\">>;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/v6/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n  future,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({\n      basename,\n      navigator,\n      static: staticProp,\n      future: {\n        v7_relativeSplatPath: false,\n        ...future,\n      },\n    }),\n    [basename, future, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key,\n      },\n      navigationType,\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n\n  warning(\n    locationContext != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (locationContext == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider children={children} value={locationContext} />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/v6/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        \"_error\" in promise\n          ? AwaitRenderStatus.error\n          : \"_data\" in promise\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/v6/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    let treePath = [...parentPath, index];\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, treePath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary:\n        element.props.ErrorBoundary != null ||\n        element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n", "import * as React from \"react\";\nimport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AgnosticPatchRoutesOnNavigationFunction,\n  AgnosticPatchRoutesOnNavigationFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  HydrationState,\n  InitialEntry,\n  JsonFunction,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  RedirectFunction,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  FutureConfig as RouterFutureConfig,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  resolvePath,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  FutureConfig,\n  IndexRouteProps,\n  LayoutRouteProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  PathRouteProps,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n} from \"./lib/components\";\nimport {\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createRoutesFromChildren,\n  renderMatches,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  NavigateOptions,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteId,\n  useRouteLoaderData,\n  useRoutes,\n  useRoutesImpl,\n} from \"./lib/hooks\";\nimport { logV6DeprecationWarnings } from \"./lib/deprecations\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  Pathname,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n  Blocker,\n  BlockerFunction,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useBlocker,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nexport type PatchRoutesOnNavigationFunctionArgs =\n  AgnosticPatchRoutesOnNavigationFunctionArgs<RouteObject, RouteMatch>;\n\nexport type PatchRoutesOnNavigationFunction =\n  AgnosticPatchRoutesOnNavigationFunction<RouteObject, RouteMatch>;\n\nfunction mapRouteProperties(route: RouteObject) {\n  let updates: Partial<RouteObject> & { hasErrorBoundary: boolean } = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null,\n  };\n\n  if (route.Component) {\n    if (__DEV__) {\n      if (route.element) {\n        warning(\n          false,\n          \"You should not include both `Component` and `element` on your route - \" +\n            \"`Component` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      element: React.createElement(route.Component),\n      Component: undefined,\n    });\n  }\n\n  if (route.HydrateFallback) {\n    if (__DEV__) {\n      if (route.hydrateFallbackElement) {\n        warning(\n          false,\n          \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" +\n            \"`HydrateFallback` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined,\n    });\n  }\n\n  if (route.ErrorBoundary) {\n    if (__DEV__) {\n      if (route.errorElement) {\n        warning(\n          false,\n          \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" +\n            \"`ErrorBoundary` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      errorElement: React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined,\n    });\n  }\n\n  return updates;\n}\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n    dataStrategy?: DataStrategyFunction;\n    patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  LocationContext as UNSAFE_LocationContext,\n  NavigationContext as UNSAFE_NavigationContext,\n  RouteContext as UNSAFE_RouteContext,\n  mapRouteProperties as UNSAFE_mapRouteProperties,\n  useRouteId as UNSAFE_useRouteId,\n  useRoutesImpl as UNSAFE_useRoutesImpl,\n  logV6DeprecationWarnings as UNSAFE_logV6DeprecationWarnings,\n};\n"], "names": ["DataRouterContext", "React", "createContext", "displayName", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "isDataRoute", "RouteErrorContext", "useHref", "to", "_temp", "relative", "useInRouterContext", "invariant", "basename", "navigator", "useContext", "hash", "pathname", "search", "useResolvedPath", "joinedPathname", "joinPaths", "createHref", "useLocation", "location", "useNavigationType", "navigationType", "useMatch", "pattern", "useMemo", "matchPath", "decodePath", "navigateEffectWarning", "useIsomorphicLayoutEffect", "cb", "isStatic", "static", "useLayoutEffect", "useNavigate", "useNavigateStable", "useNavigateUnstable", "dataRouterContext", "future", "locationPathname", "routePathnamesJson", "JSON", "stringify", "getResolveToMatches", "v7_relativeSplatPath", "activeRef", "useRef", "current", "navigate", "useCallback", "options", "process", "warning", "go", "path", "resolveTo", "parse", "replace", "push", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "createElement", "Provider", "value", "useParams", "routeMatch", "length", "params", "_temp2", "useRoutes", "routes", "locationArg", "useRoutesImpl", "dataRouterState", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "pathnameBase", "parentRoute", "route", "parentPath", "warningOnce", "endsWith", "locationFromContext", "_parsedLocationArg$pa", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "parentSegments", "split", "segments", "slice", "join", "matchRoutes", "element", "undefined", "Component", "lazy", "renderedMatches", "_renderMatches", "map", "match", "Object", "assign", "encodeLocation", "_extends", "key", "NavigationType", "Pop", "DefaultErrorComponent", "error", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "<PERSON><PERSON>rey", "preStyles", "padding", "backgroundColor", "codeStyles", "devInfo", "console", "Fragment", "style", "fontStyle", "defaultErrorElement", "RenderErrorBoundary", "constructor", "props", "revalidation", "getDerivedStateFromError", "getDerivedStateFromProps", "componentDidCatch", "errorInfo", "render", "routeContext", "children", "component", "RenderedRoute", "_ref", "staticContext", "errorElement", "Error<PERSON>ou<PERSON><PERSON>", "_deepestRenderedBoundaryId", "id", "_dataRouterState", "_future", "errors", "v7_partialHydration", "initialized", "errorIndex", "findIndex", "m", "keys", "Math", "min", "renderFallback", "fallbackIndex", "i", "HydrateFallback", "hydrateFallbackElement", "loaderData", "needsToRunLoader", "loader", "reduceRight", "index", "shouldRenderHydrateFallback", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "useDataRouterState", "useRouteContext", "useCurrentRouteId", "thisRoute", "useRouteId", "UseRouteId", "useNavigation", "UseNavigation", "navigation", "useRevalidator", "UseRevalidator", "revalidate", "router", "useMatches", "UseMatches", "convertRouteMatchToUiMatch", "useLoaderData", "UseLoaderData", "routeId", "useRouteLoaderData", "UseRouteLoaderData", "useActionData", "UseActionData", "actionData", "_state$errors", "UseRouteError", "useAsyncValue", "_data", "useAsyncError", "_error", "blockerId", "useBlocker", "shouldBlock", "UseBlocker", "blockerKey", "set<PERSON><PERSON>er<PERSON>ey", "useState", "blockerFunction", "arg", "currentLocation", "nextLocation", "historyAction", "stripBasename", "useEffect", "String", "deleteBlocker", "get<PERSON><PERSON>er", "blockers", "has", "get", "IDLE_BLOCKER", "UseNavigateStable", "fromRouteId", "alreadyWarned", "cond", "warnOnce", "warn", "logDeprecation", "flag", "msg", "link", "logV6DeprecationWarnings", "renderFuture", "routerFuture", "v7_startTransition", "v7_fetcherPersist", "v7_normalizeFormMethod", "v7_skipActionErrorRevalidation", "START_TRANSITION", "startTransitionImpl", "RouterProvider", "fallbackElement", "setStateImpl", "setState", "newState", "subscribe", "n", "opts", "preventScrollReset", "Router", "DataRoutes", "_ref2", "MemoryRouter", "_ref3", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "v5Compat", "history", "action", "listen", "Navigate", "_ref4", "jsonPath", "Outlet", "Route", "_props", "_ref5", "basenameProp", "locationProp", "staticProp", "navigationContext", "locationContext", "trailingPathname", "Routes", "_ref6", "createRoutesFromChildren", "Await", "_ref7", "resolve", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "ResolveAwait", "AwaitRenderStatus", "neverSettledPromise", "Promise", "promise", "pending", "success", "defineProperty", "renderError", "reject", "catch", "_tracked", "then", "data", "Aborted<PERSON>eferredError", "_ref8", "to<PERSON><PERSON>", "Children", "for<PERSON>ach", "isValidElement", "treePath", "type", "apply", "name", "caseSensitive", "hasErrorBou<PERSON>ry", "shouldRevalidate", "handle", "renderMatches", "mapRouteProperties", "updates", "createMemoryRouter", "createRouter", "v7_prependBasename", "hydrationData", "dataStrategy", "patchRoutesOnNavigation", "initialize"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAgBA;EACA;AA+DO,QAAMA,iBAAiB,gBAC5BC,gBAAK,CAACC,aAAa,CAAiC,IAAI,EAAC;EAC9C;IACXF,iBAAiB,CAACG,WAAW,GAAG,YAAY,CAAA;EAC9C,CAAA;AAEO,QAAMC,sBAAsB,gBAAGH,gBAAK,CAACC,aAAa,CAEvD,IAAI,EAAC;EACM;IACXE,sBAAsB,CAACD,WAAW,GAAG,iBAAiB,CAAA;EACxD,CAAA;EAEO,MAAME,YAAY,gBAAGJ,gBAAK,CAACC,aAAa,CAAwB,IAAI,CAAC,CAAA;EAC/D;IACXG,YAAY,CAACF,WAAW,GAAG,OAAO,CAAA;EACpC,CAAA;;EAWA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAmBO,QAAMG,iBAAiB,gBAAGL,gBAAK,CAACC,aAAa,CAClD,IACF,EAAC;EAEY;IACXI,iBAAiB,CAACH,WAAW,GAAG,YAAY,CAAA;EAC9C,CAAA;AAOO,QAAMI,eAAe,gBAAGN,gBAAK,CAACC,aAAa,CAChD,IACF,EAAC;EAEY;IACXK,eAAe,CAACJ,WAAW,GAAG,UAAU,CAAA;EAC1C,CAAA;QAQaK,YAAY,gBAAGP,gBAAK,CAACC,aAAa,CAAqB;EAClEO,EAAAA,MAAM,EAAE,IAAI;EACZC,EAAAA,OAAO,EAAE,EAAE;EACXC,EAAAA,WAAW,EAAE,KAAA;EACf,CAAC,EAAC;EAEW;IACXH,YAAY,CAACL,WAAW,GAAG,OAAO,CAAA;EACpC,CAAA;EAEO,MAAMS,iBAAiB,gBAAGX,gBAAK,CAACC,aAAa,CAAM,IAAI,CAAC,CAAA;EAElD;IACXU,iBAAiB,CAACT,WAAW,GAAG,YAAY,CAAA;EAC9C;;EC7HA;EACA;EACA;EACA;EACA;EACA;EACO,SAASU,OAAOA,CACrBC,EAAM,EAAAC,KAAA,EAEE;IAAA,IADR;EAAEC,IAAAA,QAAAA;EAA6C,GAAC,GAAAD,KAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,KAAA,CAAA;IAErD,CACEE,kBAAkB,EAAE,GADtBC,uBAAS,CAEP,KAAA;EACA;EAAA,EAAA,oEAAA,CAAA,CAHO,GAAA,KAAA,CAAA,CAAA;IAOT,IAAI;MAAEC,QAAQ;EAAEC,IAAAA,SAAAA;EAAU,GAAC,GAAGnB,gBAAK,CAACoB,UAAU,CAACf,iBAAiB,CAAC,CAAA;IACjE,IAAI;MAAEgB,IAAI;MAAEC,QAAQ;EAAEC,IAAAA,MAAAA;EAAO,GAAC,GAAGC,eAAe,CAACX,EAAE,EAAE;EAAEE,IAAAA,QAAAA;EAAS,GAAC,CAAC,CAAA;IAElE,IAAIU,cAAc,GAAGH,QAAQ,CAAA;;EAE7B;EACA;EACA;EACA;IACA,IAAIJ,QAAQ,KAAK,GAAG,EAAE;EACpBO,IAAAA,cAAc,GACZH,QAAQ,KAAK,GAAG,GAAGJ,QAAQ,GAAGQ,gBAAS,CAAC,CAACR,QAAQ,EAAEI,QAAQ,CAAC,CAAC,CAAA;EACjE,GAAA;IAEA,OAAOH,SAAS,CAACQ,UAAU,CAAC;EAAEL,IAAAA,QAAQ,EAAEG,cAAc;MAAEF,MAAM;EAAEF,IAAAA,IAAAA;EAAK,GAAC,CAAC,CAAA;EACzE,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACO,SAASL,kBAAkBA,GAAY;EAC5C,EAAA,OAAOhB,gBAAK,CAACoB,UAAU,CAACd,eAAe,CAAC,IAAI,IAAI,CAAA;EAClD,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASsB,WAAWA,GAAa;IACtC,CACEZ,kBAAkB,EAAE,GADtBC,uBAAS,CAEP,KAAA;EACA;EAAA,EAAA,wEAAA,CAAA,CAHO,GAAA,KAAA,CAAA,CAAA;EAOT,EAAA,OAAOjB,gBAAK,CAACoB,UAAU,CAACd,eAAe,CAAC,CAACuB,QAAQ,CAAA;EACnD,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO,SAASC,iBAAiBA,GAAmB;EAClD,EAAA,OAAO9B,gBAAK,CAACoB,UAAU,CAACd,eAAe,CAAC,CAACyB,cAAc,CAAA;EACzD,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASC,QAAQA,CAGtBC,OAAiC,EAA8B;IAC/D,CACEjB,kBAAkB,EAAE,GADtBC,uBAAS,CAEP,KAAA;EACA;EAAA,EAAA,qEAAA,CAAA,CAHO,GAAA,KAAA,CAAA,CAAA;IAOT,IAAI;EAAEK,IAAAA,QAAAA;KAAU,GAAGM,WAAW,EAAE,CAAA;IAChC,OAAO5B,gBAAK,CAACkC,OAAO,CAClB,MAAMC,gBAAS,CAAiBF,OAAO,EAAEG,wBAAU,CAACd,QAAQ,CAAC,CAAC,EAC9D,CAACA,QAAQ,EAAEW,OAAO,CACpB,CAAC,CAAA;EACH,CAAA;;EAEA;EACA;EACA;;EAMA,MAAMI,qBAAqB,GACzB,8DACmC,GAAA,mCAAA,CAAA;;EAErC;EACA,SAASC,yBAAyBA,CAChCC,EAA+C,EAC/C;IACA,IAAIC,QAAQ,GAAGxC,gBAAK,CAACoB,UAAU,CAACf,iBAAiB,CAAC,CAACoC,MAAM,CAAA;IACzD,IAAI,CAACD,QAAQ,EAAE;EACb;EACA;EACA;EACAxC,IAAAA,gBAAK,CAAC0C,eAAe,CAACH,EAAE,CAAC,CAAA;EAC3B,GAAA;EACF,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO,SAASI,WAAWA,GAAqB;IAC9C,IAAI;EAAEjC,IAAAA,WAAAA;EAAY,GAAC,GAAGV,gBAAK,CAACoB,UAAU,CAACb,YAAY,CAAC,CAAA;EACpD;EACA;IACA,OAAOG,WAAW,GAAGkC,iBAAiB,EAAE,GAAGC,mBAAmB,EAAE,CAAA;EAClE,CAAA;EAEA,SAASA,mBAAmBA,GAAqB;IAC/C,CACE7B,kBAAkB,EAAE,GADtBC,uBAAS,CAEP,KAAA;EACA;EAAA,EAAA,wEAAA,CAAA,CAHO,GAAA,KAAA,CAAA,CAAA;EAOT,EAAA,IAAI6B,iBAAiB,GAAG9C,gBAAK,CAACoB,UAAU,CAACrB,iBAAiB,CAAC,CAAA;IAC3D,IAAI;MAAEmB,QAAQ;MAAE6B,MAAM;EAAE5B,IAAAA,SAAAA;EAAU,GAAC,GAAGnB,gBAAK,CAACoB,UAAU,CAACf,iBAAiB,CAAC,CAAA;IACzE,IAAI;EAAEI,IAAAA,OAAAA;EAAQ,GAAC,GAAGT,gBAAK,CAACoB,UAAU,CAACb,YAAY,CAAC,CAAA;IAChD,IAAI;EAAEe,IAAAA,QAAQ,EAAE0B,gBAAAA;KAAkB,GAAGpB,WAAW,EAAE,CAAA;EAElD,EAAA,IAAIqB,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CACrCC,iCAAmB,CAAC3C,OAAO,EAAEsC,MAAM,CAACM,oBAAoB,CAC1D,CAAC,CAAA;EAED,EAAA,IAAIC,SAAS,GAAGtD,gBAAK,CAACuD,MAAM,CAAC,KAAK,CAAC,CAAA;EACnCjB,EAAAA,yBAAyB,CAAC,MAAM;MAC9BgB,SAAS,CAACE,OAAO,GAAG,IAAI,CAAA;EAC1B,GAAC,CAAC,CAAA;IAEF,IAAIC,QAA0B,GAAGzD,gBAAK,CAAC0D,WAAW,CAChD,UAAC7C,EAAe,EAAE8C,OAAwB,EAAU;EAAA,IAAA,IAAlCA,OAAwB,KAAA,KAAA,CAAA,EAAA;QAAxBA,OAAwB,GAAG,EAAE,CAAA;EAAA,KAAA;EAC7CC,IAAAC,qBAAO,CAACP,SAAS,CAACE,OAAO,EAAEnB,qBAAqB,CAAC,CAAA,CAAA;;EAEjD;EACA;EACA,IAAA,IAAI,CAACiB,SAAS,CAACE,OAAO,EAAE,OAAA;EAExB,IAAA,IAAI,OAAO3C,EAAE,KAAK,QAAQ,EAAE;EAC1BM,MAAAA,SAAS,CAAC2C,EAAE,CAACjD,EAAE,CAAC,CAAA;EAChB,MAAA,OAAA;EACF,KAAA;MAEA,IAAIkD,IAAI,GAAGC,gBAAS,CAClBnD,EAAE,EACFqC,IAAI,CAACe,KAAK,CAAChB,kBAAkB,CAAC,EAC9BD,gBAAgB,EAChBW,OAAO,CAAC5C,QAAQ,KAAK,MACvB,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA,IAAA,IAAI+B,iBAAiB,IAAI,IAAI,IAAI5B,QAAQ,KAAK,GAAG,EAAE;QACjD6C,IAAI,CAACzC,QAAQ,GACXyC,IAAI,CAACzC,QAAQ,KAAK,GAAG,GACjBJ,QAAQ,GACRQ,gBAAS,CAAC,CAACR,QAAQ,EAAE6C,IAAI,CAACzC,QAAQ,CAAC,CAAC,CAAA;EAC5C,KAAA;MAEA,CAAC,CAAC,CAACqC,OAAO,CAACO,OAAO,GAAG/C,SAAS,CAAC+C,OAAO,GAAG/C,SAAS,CAACgD,IAAI,EACrDJ,IAAI,EACJJ,OAAO,CAACS,KAAK,EACbT,OACF,CAAC,CAAA;EACH,GAAC,EACD,CACEzC,QAAQ,EACRC,SAAS,EACT8B,kBAAkB,EAClBD,gBAAgB,EAChBF,iBAAiB,CAErB,CAAC,CAAA;EAED,EAAA,OAAOW,QAAQ,CAAA;EACjB,CAAA;EAEA,MAAMY,aAAa,gBAAGrE,gBAAK,CAACC,aAAa,CAAU,IAAI,CAAC,CAAA;;EAExD;EACA;EACA;EACA;EACA;EACO,SAASqE,gBAAgBA,GAA+B;EAC7D,EAAA,OAAOtE,gBAAK,CAACoB,UAAU,CAACiD,aAAa,CAAC,CAAA;EACxC,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO,SAASE,SAASA,CAACC,OAAiB,EAA6B;IACtE,IAAIhE,MAAM,GAAGR,gBAAK,CAACoB,UAAU,CAACb,YAAY,CAAC,CAACC,MAAM,CAAA;EAClD,EAAA,IAAIA,MAAM,EAAE;EACV,IAAA,oBACER,gBAAA,CAAAyE,aAAA,CAACJ,aAAa,CAACK,QAAQ,EAAA;EAACC,MAAAA,KAAK,EAAEH,OAAAA;EAAQ,KAAA,EAAEhE,MAA+B,CAAC,CAAA;EAE7E,GAAA;EACA,EAAA,OAAOA,MAAM,CAAA;EACf,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO,SAASoE,SAASA,GAIvB;IACA,IAAI;EAAEnE,IAAAA,OAAAA;EAAQ,GAAC,GAAGT,gBAAK,CAACoB,UAAU,CAACb,YAAY,CAAC,CAAA;IAChD,IAAIsE,UAAU,GAAGpE,OAAO,CAACA,OAAO,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAA;EAC5C,EAAA,OAAOD,UAAU,GAAIA,UAAU,CAACE,MAAM,GAAW,EAAE,CAAA;EACrD,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACO,SAASvD,eAAeA,CAC7BX,EAAM,EAAAmE,MAAA,EAEA;IAAA,IADN;EAAEjE,IAAAA,QAAAA;EAA6C,GAAC,GAAAiE,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;IAErD,IAAI;EAAEjC,IAAAA,MAAAA;EAAO,GAAC,GAAG/C,gBAAK,CAACoB,UAAU,CAACf,iBAAiB,CAAC,CAAA;IACpD,IAAI;EAAEI,IAAAA,OAAAA;EAAQ,GAAC,GAAGT,gBAAK,CAACoB,UAAU,CAACb,YAAY,CAAC,CAAA;IAChD,IAAI;EAAEe,IAAAA,QAAQ,EAAE0B,gBAAAA;KAAkB,GAAGpB,WAAW,EAAE,CAAA;EAClD,EAAA,IAAIqB,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CACrCC,iCAAmB,CAAC3C,OAAO,EAAEsC,MAAM,CAACM,oBAAoB,CAC1D,CAAC,CAAA;EAED,EAAA,OAAOrD,gBAAK,CAACkC,OAAO,CAClB,MACE8B,gBAAS,CACPnD,EAAE,EACFqC,IAAI,CAACe,KAAK,CAAChB,kBAAkB,CAAC,EAC9BD,gBAAgB,EAChBjC,QAAQ,KAAK,MACf,CAAC,EACH,CAACF,EAAE,EAAEoC,kBAAkB,EAAED,gBAAgB,EAAEjC,QAAQ,CACrD,CAAC,CAAA;EACH,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASkE,SAASA,CACvBC,MAAqB,EACrBC,WAAwC,EACb;EAC3B,EAAA,OAAOC,aAAa,CAACF,MAAM,EAAEC,WAAW,CAAC,CAAA;EAC3C,CAAA;;EAEA;EACO,SAASC,aAAaA,CAC3BF,MAAqB,EACrBC,WAAwC,EACxCE,eAAsC,EACtCtC,MAA8B,EACH;IAC3B,CACE/B,kBAAkB,EAAE,GADtBC,uBAAS,CAEP,KAAA;EACA;EAAA,EAAA,sEAAA,CAAA,CAHO,GAAA,KAAA,CAAA,CAAA;IAOT,IAAI;EAAEE,IAAAA,SAAAA;EAAU,GAAC,GAAGnB,gBAAK,CAACoB,UAAU,CAACf,iBAAiB,CAAC,CAAA;IACvD,IAAI;EAAEI,IAAAA,OAAO,EAAE6E,aAAAA;EAAc,GAAC,GAAGtF,gBAAK,CAACoB,UAAU,CAACb,YAAY,CAAC,CAAA;IAC/D,IAAIsE,UAAU,GAAGS,aAAa,CAACA,aAAa,CAACR,MAAM,GAAG,CAAC,CAAC,CAAA;IACxD,IAAIS,YAAY,GAAGV,UAAU,GAAGA,UAAU,CAACE,MAAM,GAAG,EAAE,CAAA;IACtD,IAAIS,cAAc,GAAGX,UAAU,GAAGA,UAAU,CAACvD,QAAQ,GAAG,GAAG,CAAA;IAC3D,IAAImE,kBAAkB,GAAGZ,UAAU,GAAGA,UAAU,CAACa,YAAY,GAAG,GAAG,CAAA;EACnE,EAAA,IAAIC,WAAW,GAAGd,UAAU,IAAIA,UAAU,CAACe,KAAK,CAAA;EAEhD,EAAa;EACX;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;MACA,IAAIC,UAAU,GAAIF,WAAW,IAAIA,WAAW,CAAC5B,IAAI,IAAK,EAAE,CAAA;EACxD+B,IAAAA,WAAW,CACTN,cAAc,EACd,CAACG,WAAW,IAAIE,UAAU,CAACE,QAAQ,CAAC,GAAG,CAAC,EACxC,gEAAA,IAAA,IAAA,GACMP,cAAc,GAAyBK,0BAAAA,GAAAA,UAAU,GAAc,eAAA,CAAA,GAAA,sEACC,GACH,iEAAA,GAAA,+BAClC,IACUA,yCAAAA,GAAAA,UAAU,oBAAe,IACzDA,SAAAA,IAAAA,UAAU,KAAK,GAAG,GAAG,GAAG,GAAMA,UAAU,GAAA,IAAI,WACzD,CAAC,CAAA;EACH,GAAA;EAEA,EAAA,IAAIG,mBAAmB,GAAGpE,WAAW,EAAE,CAAA;EAEvC,EAAA,IAAIC,QAAQ,CAAA;EACZ,EAAA,IAAIsD,WAAW,EAAE;EAAA,IAAA,IAAAc,qBAAA,CAAA;EACf,IAAA,IAAIC,iBAAiB,GACnB,OAAOf,WAAW,KAAK,QAAQ,GAAGgB,gBAAS,CAAChB,WAAW,CAAC,GAAGA,WAAW,CAAA;EAExE,IAAA,EACEM,kBAAkB,KAAK,GAAG,KAAA,CAAAQ,qBAAA,GACxBC,iBAAiB,CAAC5E,QAAQ,qBAA1B2E,qBAAA,CAA4BG,UAAU,CAACX,kBAAkB,CAAC,CAAA,CAAA,GAF9DxE,uBAAS,QAGP,2FACmF,GAAA,iFAAA,IAAA,+DAAA,GAClBwE,kBAAkB,GAAA,KAAA,CAAI,wBACpES,iBAAiB,CAAC5E,QAAQ,GAAA,sCAAA,CAAuC,EAN7E,GAAA,KAAA,CAAA,CAAA;EASTO,IAAAA,QAAQ,GAAGqE,iBAAiB,CAAA;EAC9B,GAAC,MAAM;EACLrE,IAAAA,QAAQ,GAAGmE,mBAAmB,CAAA;EAChC,GAAA;EAEA,EAAA,IAAI1E,QAAQ,GAAGO,QAAQ,CAACP,QAAQ,IAAI,GAAG,CAAA;IAEvC,IAAI+E,iBAAiB,GAAG/E,QAAQ,CAAA;IAChC,IAAImE,kBAAkB,KAAK,GAAG,EAAE;EAC9B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAA,IAAIa,cAAc,GAAGb,kBAAkB,CAACvB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACqC,KAAK,CAAC,GAAG,CAAC,CAAA;EACrE,IAAA,IAAIC,QAAQ,GAAGlF,QAAQ,CAAC4C,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACqC,KAAK,CAAC,GAAG,CAAC,CAAA;EACrDF,IAAAA,iBAAiB,GAAG,GAAG,GAAGG,QAAQ,CAACC,KAAK,CAACH,cAAc,CAACxB,MAAM,CAAC,CAAC4B,IAAI,CAAC,GAAG,CAAC,CAAA;EAC3E,GAAA;EAEA,EAAA,IAAIjG,OAAO,GAAGkG,kBAAW,CAACzB,MAAM,EAAE;EAAE5D,IAAAA,QAAQ,EAAE+E,iBAAAA;EAAkB,GAAC,CAAC,CAAA;EAElE,EAAa;MACXxC,qBAAO,CACL8B,WAAW,IAAIlF,OAAO,IAAI,IAAI,oCACCoB,QAAQ,CAACP,QAAQ,GAAGO,QAAQ,CAACN,MAAM,GAAGM,QAAQ,CAACR,IAAI,GAAA,KACpF,CAAC,CAAA,CAAA;EAEDuC,IAAAC,qBAAO,CACLpD,OAAO,IAAI,IAAI,IACbA,OAAO,CAACA,OAAO,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACc,KAAK,CAACgB,OAAO,KAAKC,SAAS,IACvDpG,OAAO,CAACA,OAAO,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACc,KAAK,CAACkB,SAAS,KAAKD,SAAS,IACzDpG,OAAO,CAACA,OAAO,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACc,KAAK,CAACmB,IAAI,KAAKF,SAAS,EACtD,mCAAA,GAAmChF,QAAQ,CAACP,QAAQ,GAAGO,QAAQ,CAACN,MAAM,GAAGM,QAAQ,CAACR,IAAI,GAAA,KAAA,GAAA,wFACI,0DAE5F,CAAC,CAAA,CAAA;EACH,GAAA;IAEA,IAAI2F,eAAe,GAAGC,cAAc,CAClCxG,OAAO,IACLA,OAAO,CAACyG,GAAG,CAAEC,KAAK,IAChBC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEF,KAAK,EAAE;EACvBpC,IAAAA,MAAM,EAAEqC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE9B,YAAY,EAAE4B,KAAK,CAACpC,MAAM,CAAC;EACrDzD,IAAAA,QAAQ,EAAEI,gBAAS,CAAC,CAClB+D,kBAAkB;EAClB;EACAtE,IAAAA,SAAS,CAACmG,cAAc,GACpBnG,SAAS,CAACmG,cAAc,CAACH,KAAK,CAAC7F,QAAQ,CAAC,CAACA,QAAQ,GACjD6F,KAAK,CAAC7F,QAAQ,CACnB,CAAC;EACFoE,IAAAA,YAAY,EACVyB,KAAK,CAACzB,YAAY,KAAK,GAAG,GACtBD,kBAAkB,GAClB/D,gBAAS,CAAC,CACR+D,kBAAkB;EAClB;EACAtE,IAAAA,SAAS,CAACmG,cAAc,GACpBnG,SAAS,CAACmG,cAAc,CAACH,KAAK,CAACzB,YAAY,CAAC,CAACpE,QAAQ,GACrD6F,KAAK,CAACzB,YAAY,CACvB,CAAA;KACR,CACH,CAAC,EACHJ,aAAa,EACbD,eAAe,EACftC,MACF,CAAC,CAAA;;EAED;EACA;EACA;IACA,IAAIoC,WAAW,IAAI6B,eAAe,EAAE;EAClC,IAAA,oBACEhH,gBAAA,CAAAyE,aAAA,CAACnE,eAAe,CAACoE,QAAQ,EAAA;EACvBC,MAAAA,KAAK,EAAE;EACL9C,QAAAA,QAAQ,EAAA0F,QAAA,CAAA;EACNjG,UAAAA,QAAQ,EAAE,GAAG;EACbC,UAAAA,MAAM,EAAE,EAAE;EACVF,UAAAA,IAAI,EAAE,EAAE;EACR+C,UAAAA,KAAK,EAAE,IAAI;EACXoD,UAAAA,GAAG,EAAE,SAAA;EAAS,SAAA,EACX3F,QAAQ,CACZ;UACDE,cAAc,EAAE0F,aAAc,CAACC,GAAAA;EACjC,OAAA;EAAE,KAAA,EAEDV,eACuB,CAAC,CAAA;EAE/B,GAAA;EAEA,EAAA,OAAOA,eAAe,CAAA;EACxB,CAAA;EAEA,SAASW,qBAAqBA,GAAG;EAC/B,EAAA,IAAIC,KAAK,GAAGC,aAAa,EAAE,CAAA;EAC3B,EAAA,IAAIC,OAAO,GAAGC,2BAAoB,CAACH,KAAK,CAAC,GAClCA,KAAK,CAACI,MAAM,GAAIJ,GAAAA,GAAAA,KAAK,CAACK,UAAU,GACnCL,KAAK,YAAYM,KAAK,GACtBN,KAAK,CAACE,OAAO,GACb5E,IAAI,CAACC,SAAS,CAACyE,KAAK,CAAC,CAAA;IACzB,IAAIO,KAAK,GAAGP,KAAK,YAAYM,KAAK,GAAGN,KAAK,CAACO,KAAK,GAAG,IAAI,CAAA;IACvD,IAAIC,SAAS,GAAG,wBAAwB,CAAA;EACxC,EAAA,IAAIC,SAAS,GAAG;EAAEC,IAAAA,OAAO,EAAE,QAAQ;EAAEC,IAAAA,eAAe,EAAEH,SAAAA;KAAW,CAAA;EACjE,EAAA,IAAII,UAAU,GAAG;EAAEF,IAAAA,OAAO,EAAE,SAAS;EAAEC,IAAAA,eAAe,EAAEH,SAAAA;KAAW,CAAA;IAEnE,IAAIK,OAAO,GAAG,IAAI,CAAA;EAClB,EAAa;EACXC,IAAAA,OAAO,CAACd,KAAK,CACX,sDAAsD,EACtDA,KACF,CAAC,CAAA;MAEDa,OAAO,gBACLzI,gBAAA,CAAAyE,aAAA,CAAAzE,gBAAA,CAAA2I,QAAA,EACE3I,IAAAA,eAAAA,gBAAA,CAAAyE,aAAA,YAAG,yCAAsB,CAAC,eAC1BzE,gBAAA,CAAAyE,aAAA,YAAG,8FAEqB,eAAAzE,gBAAA,CAAAyE,aAAA,CAAA,MAAA,EAAA;EAAMmE,MAAAA,KAAK,EAAEJ,UAAAA;OAAY,EAAA,eAAmB,CAAC,EAAG,KAAA,EAAC,GAAG,eAC1ExI,gBAAA,CAAAyE,aAAA,CAAA,MAAA,EAAA;EAAMmE,MAAAA,KAAK,EAAEJ,UAAAA;EAAW,KAAA,EAAC,cAAkB,CAAC,EAC3C,sBAAA,CACH,CACH,CAAA;EACH,GAAA;EAEA,EAAA,oBACExI,gBAAA,CAAAyE,aAAA,CAAAzE,gBAAA,CAAA2I,QAAA,EAAA,IAAA,eACE3I,gBAAA,CAAAyE,aAAA,CAAI,IAAA,EAAA,IAAA,EAAA,+BAAiC,CAAC,eACtCzE,gBAAA,CAAAyE,aAAA,CAAA,IAAA,EAAA;EAAImE,IAAAA,KAAK,EAAE;EAAEC,MAAAA,SAAS,EAAE,QAAA;EAAS,KAAA;EAAE,GAAA,EAAEf,OAAY,CAAC,EACjDK,KAAK,gBAAGnI,gBAAA,CAAAyE,aAAA,CAAA,KAAA,EAAA;EAAKmE,IAAAA,KAAK,EAAEP,SAAAA;EAAU,GAAA,EAAEF,KAAW,CAAC,GAAG,IAAI,EACnDM,OACD,CAAC,CAAA;EAEP,CAAA;EAEA,MAAMK,mBAAmB,gBAAG9I,gBAAA,CAAAyE,aAAA,CAACkD,qBAAqB,MAAE,CAAC,CAAA;EAgB9C,MAAMoB,mBAAmB,SAAS/I,gBAAK,CAAC8G,SAAS,CAGtD;IACAkC,WAAWA,CAACC,KAA+B,EAAE;MAC3C,KAAK,CAACA,KAAK,CAAC,CAAA;MACZ,IAAI,CAAC7E,KAAK,GAAG;QACXvC,QAAQ,EAAEoH,KAAK,CAACpH,QAAQ;QACxBqH,YAAY,EAAED,KAAK,CAACC,YAAY;QAChCtB,KAAK,EAAEqB,KAAK,CAACrB,KAAAA;OACd,CAAA;EACH,GAAA;IAEA,OAAOuB,wBAAwBA,CAACvB,KAAU,EAAE;MAC1C,OAAO;EAAEA,MAAAA,KAAK,EAAEA,KAAAA;OAAO,CAAA;EACzB,GAAA;EAEA,EAAA,OAAOwB,wBAAwBA,CAC7BH,KAA+B,EAC/B7E,KAA+B,EAC/B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAA,IACEA,KAAK,CAACvC,QAAQ,KAAKoH,KAAK,CAACpH,QAAQ,IAChCuC,KAAK,CAAC8E,YAAY,KAAK,MAAM,IAAID,KAAK,CAACC,YAAY,KAAK,MAAO,EAChE;QACA,OAAO;UACLtB,KAAK,EAAEqB,KAAK,CAACrB,KAAK;UAClB/F,QAAQ,EAAEoH,KAAK,CAACpH,QAAQ;UACxBqH,YAAY,EAAED,KAAK,CAACC,YAAAA;SACrB,CAAA;EACH,KAAA;;EAEA;EACA;EACA;EACA;MACA,OAAO;EACLtB,MAAAA,KAAK,EAAEqB,KAAK,CAACrB,KAAK,KAAKf,SAAS,GAAGoC,KAAK,CAACrB,KAAK,GAAGxD,KAAK,CAACwD,KAAK;QAC5D/F,QAAQ,EAAEuC,KAAK,CAACvC,QAAQ;EACxBqH,MAAAA,YAAY,EAAED,KAAK,CAACC,YAAY,IAAI9E,KAAK,CAAC8E,YAAAA;OAC3C,CAAA;EACH,GAAA;EAEAG,EAAAA,iBAAiBA,CAACzB,KAAU,EAAE0B,SAAc,EAAE;MAC5CZ,OAAO,CAACd,KAAK,CACX,uDAAuD,EACvDA,KAAK,EACL0B,SACF,CAAC,CAAA;EACH,GAAA;EAEAC,EAAAA,MAAMA,GAAG;EACP,IAAA,OAAO,IAAI,CAACnF,KAAK,CAACwD,KAAK,KAAKf,SAAS,gBACnC7G,gBAAA,CAAAyE,aAAA,CAAClE,YAAY,CAACmE,QAAQ,EAAA;EAACC,MAAAA,KAAK,EAAE,IAAI,CAACsE,KAAK,CAACO,YAAAA;EAAa,KAAA,eACpDxJ,gBAAA,CAAAyE,aAAA,CAAC9D,iBAAiB,CAAC+D,QAAQ,EAAA;EACzBC,MAAAA,KAAK,EAAE,IAAI,CAACP,KAAK,CAACwD,KAAM;EACxB6B,MAAAA,QAAQ,EAAE,IAAI,CAACR,KAAK,CAACS,SAAAA;EAAU,KAChC,CACoB,CAAC,GAExB,IAAI,CAACT,KAAK,CAACQ,QACZ,CAAA;EACH,GAAA;EACF,CAAA;EAQA,SAASE,aAAaA,CAAAC,IAAA,EAAwD;IAAA,IAAvD;MAAEJ,YAAY;MAAErC,KAAK;EAAEsC,IAAAA,QAAAA;EAA6B,GAAC,GAAAG,IAAA,CAAA;EAC1E,EAAA,IAAI9G,iBAAiB,GAAG9C,gBAAK,CAACoB,UAAU,CAACrB,iBAAiB,CAAC,CAAA;;EAE3D;EACA;IACA,IACE+C,iBAAiB,IACjBA,iBAAiB,CAACL,MAAM,IACxBK,iBAAiB,CAAC+G,aAAa,KAC9B1C,KAAK,CAACvB,KAAK,CAACkE,YAAY,IAAI3C,KAAK,CAACvB,KAAK,CAACmE,aAAa,CAAC,EACvD;MACAjH,iBAAiB,CAAC+G,aAAa,CAACG,0BAA0B,GAAG7C,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAA;EAC7E,GAAA;EAEA,EAAA,oBACEjK,gBAAA,CAAAyE,aAAA,CAAClE,YAAY,CAACmE,QAAQ,EAAA;EAACC,IAAAA,KAAK,EAAE6E,YAAAA;EAAa,GAAA,EACxCC,QACoB,CAAC,CAAA;EAE5B,CAAA;EAEO,SAASxC,cAAcA,CAC5BxG,OAA4B,EAC5B6E,aAA2B,EAC3BD,eAA4C,EAC5CtC,MAAoC,EACT;EAAA,EAAA,IAAAmH,gBAAA,CAAA;EAAA,EAAA,IAH3B5E,aAA2B,KAAA,KAAA,CAAA,EAAA;EAA3BA,IAAAA,aAA2B,GAAG,EAAE,CAAA;EAAA,GAAA;EAAA,EAAA,IAChCD,eAA4C,KAAA,KAAA,CAAA,EAAA;EAA5CA,IAAAA,eAA4C,GAAG,IAAI,CAAA;EAAA,GAAA;EAAA,EAAA,IACnDtC,MAAoC,KAAA,KAAA,CAAA,EAAA;EAApCA,IAAAA,MAAoC,GAAG,IAAI,CAAA;EAAA,GAAA;IAE3C,IAAItC,OAAO,IAAI,IAAI,EAAE;EAAA,IAAA,IAAA0J,OAAA,CAAA;MACnB,IAAI,CAAC9E,eAAe,EAAE;EACpB,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;MAEA,IAAIA,eAAe,CAAC+E,MAAM,EAAE;EAC1B;EACA;QACA3J,OAAO,GAAG4E,eAAe,CAAC5E,OAA2B,CAAA;EACvD,KAAC,MAAM,IACL,CAAA0J,OAAA,GAAApH,MAAM,KAANoH,IAAAA,IAAAA,OAAA,CAAQE,mBAAmB,IAC3B/E,aAAa,CAACR,MAAM,KAAK,CAAC,IAC1B,CAACO,eAAe,CAACiF,WAAW,IAC5BjF,eAAe,CAAC5E,OAAO,CAACqE,MAAM,GAAG,CAAC,EAClC;EACA;EACA;EACA;EACA;EACA;EACA;QACArE,OAAO,GAAG4E,eAAe,CAAC5E,OAA2B,CAAA;EACvD,KAAC,MAAM;EACL,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;EACF,GAAA;IAEA,IAAIuG,eAAe,GAAGvG,OAAO,CAAA;;EAE7B;IACA,IAAI2J,MAAM,IAAAF,gBAAA,GAAG7E,eAAe,KAAf6E,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAA,CAAiBE,MAAM,CAAA;IACpC,IAAIA,MAAM,IAAI,IAAI,EAAE;MAClB,IAAIG,UAAU,GAAGvD,eAAe,CAACwD,SAAS,CACvCC,CAAC,IAAKA,CAAC,CAAC7E,KAAK,CAACqE,EAAE,IAAI,CAAAG,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAGK,CAAC,CAAC7E,KAAK,CAACqE,EAAE,CAAC,MAAKpD,SAChD,CAAC,CAAA;MACD,EACE0D,UAAU,IAAI,CAAC,CAAA3G,GADjB3C,uBAAS,CAAA,KAAA,EAAA,2DAAA,GAEqDmG,MAAM,CAACsD,IAAI,CACrEN,MACF,CAAC,CAAC1D,IAAI,CAAC,GAAG,CAAC,CAAA,CAJJ,GAAA,KAAA,CAAA,CAAA;EAMTM,IAAAA,eAAe,GAAGA,eAAe,CAACP,KAAK,CACrC,CAAC,EACDkE,IAAI,CAACC,GAAG,CAAC5D,eAAe,CAAClC,MAAM,EAAEyF,UAAU,GAAG,CAAC,CACjD,CAAC,CAAA;EACH,GAAA;;EAEA;EACA;IACA,IAAIM,cAAc,GAAG,KAAK,CAAA;IAC1B,IAAIC,aAAa,GAAG,CAAC,CAAC,CAAA;EACtB,EAAA,IAAIzF,eAAe,IAAItC,MAAM,IAAIA,MAAM,CAACsH,mBAAmB,EAAE;EAC3D,IAAA,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/D,eAAe,CAAClC,MAAM,EAAEiG,CAAC,EAAE,EAAE;EAC/C,MAAA,IAAI5D,KAAK,GAAGH,eAAe,CAAC+D,CAAC,CAAC,CAAA;EAC9B;QACA,IAAI5D,KAAK,CAACvB,KAAK,CAACoF,eAAe,IAAI7D,KAAK,CAACvB,KAAK,CAACqF,sBAAsB,EAAE;EACrEH,QAAAA,aAAa,GAAGC,CAAC,CAAA;EACnB,OAAA;EAEA,MAAA,IAAI5D,KAAK,CAACvB,KAAK,CAACqE,EAAE,EAAE;UAClB,IAAI;YAAEiB,UAAU;EAAEd,UAAAA,MAAAA;EAAO,SAAC,GAAG/E,eAAe,CAAA;EAC5C,QAAA,IAAI8F,gBAAgB,GAClBhE,KAAK,CAACvB,KAAK,CAACwF,MAAM,IAClBF,UAAU,CAAC/D,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAC,KAAKpD,SAAS,KACvC,CAACuD,MAAM,IAAIA,MAAM,CAACjD,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAC,KAAKpD,SAAS,CAAC,CAAA;EACnD,QAAA,IAAIM,KAAK,CAACvB,KAAK,CAACmB,IAAI,IAAIoE,gBAAgB,EAAE;EACxC;EACA;EACA;EACAN,UAAAA,cAAc,GAAG,IAAI,CAAA;YACrB,IAAIC,aAAa,IAAI,CAAC,EAAE;cACtB9D,eAAe,GAAGA,eAAe,CAACP,KAAK,CAAC,CAAC,EAAEqE,aAAa,GAAG,CAAC,CAAC,CAAA;EAC/D,WAAC,MAAM;EACL9D,YAAAA,eAAe,GAAG,CAACA,eAAe,CAAC,CAAC,CAAC,CAAC,CAAA;EACxC,WAAA;EACA,UAAA,MAAA;EACF,SAAA;EACF,OAAA;EACF,KAAA;EACF,GAAA;IAEA,OAAOA,eAAe,CAACqE,WAAW,CAAC,CAAC7K,MAAM,EAAE2G,KAAK,EAAEmE,KAAK,KAAK;EAC3D;EACA,IAAA,IAAI1D,KAAU,CAAA;MACd,IAAI2D,2BAA2B,GAAG,KAAK,CAAA;MACvC,IAAIzB,YAAoC,GAAG,IAAI,CAAA;MAC/C,IAAImB,sBAA8C,GAAG,IAAI,CAAA;EACzD,IAAA,IAAI5F,eAAe,EAAE;EACnBuC,MAAAA,KAAK,GAAGwC,MAAM,IAAIjD,KAAK,CAACvB,KAAK,CAACqE,EAAE,GAAGG,MAAM,CAACjD,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAC,GAAGpD,SAAS,CAAA;EACrEiD,MAAAA,YAAY,GAAG3C,KAAK,CAACvB,KAAK,CAACkE,YAAY,IAAIhB,mBAAmB,CAAA;EAE9D,MAAA,IAAI+B,cAAc,EAAE;EAClB,QAAA,IAAIC,aAAa,GAAG,CAAC,IAAIQ,KAAK,KAAK,CAAC,EAAE;EACpCxF,UAAAA,WAAW,CACT,gBAAgB,EAChB,KAAK,EACL,0EACF,CAAC,CAAA;EACDyF,UAAAA,2BAA2B,GAAG,IAAI,CAAA;EAClCN,UAAAA,sBAAsB,GAAG,IAAI,CAAA;EAC/B,SAAC,MAAM,IAAIH,aAAa,KAAKQ,KAAK,EAAE;EAClCC,UAAAA,2BAA2B,GAAG,IAAI,CAAA;EAClCN,UAAAA,sBAAsB,GAAG9D,KAAK,CAACvB,KAAK,CAACqF,sBAAsB,IAAI,IAAI,CAAA;EACrE,SAAA;EACF,OAAA;EACF,KAAA;EAEA,IAAA,IAAIxK,OAAO,GAAG6E,aAAa,CAACkG,MAAM,CAACxE,eAAe,CAACP,KAAK,CAAC,CAAC,EAAE6E,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;MACvE,IAAIG,WAAW,GAAGA,MAAM;EACtB,MAAA,IAAIhC,QAAyB,CAAA;EAC7B,MAAA,IAAI7B,KAAK,EAAE;EACT6B,QAAAA,QAAQ,GAAGK,YAAY,CAAA;SACxB,MAAM,IAAIyB,2BAA2B,EAAE;EACtC9B,QAAAA,QAAQ,GAAGwB,sBAAsB,CAAA;EACnC,OAAC,MAAM,IAAI9D,KAAK,CAACvB,KAAK,CAACkB,SAAS,EAAE;EAChC;EACA;EACA;EACA;EACA;EACA;UACA2C,QAAQ,gBAAGzJ,gBAAA,CAAAyE,aAAA,CAAC0C,KAAK,CAACvB,KAAK,CAACkB,SAAS,EAAA,IAAE,CAAC,CAAA;EACtC,OAAC,MAAM,IAAIK,KAAK,CAACvB,KAAK,CAACgB,OAAO,EAAE;EAC9B6C,QAAAA,QAAQ,GAAGtC,KAAK,CAACvB,KAAK,CAACgB,OAAO,CAAA;EAChC,OAAC,MAAM;EACL6C,QAAAA,QAAQ,GAAGjJ,MAAM,CAAA;EACnB,OAAA;EACA,MAAA,oBACER,gBAAA,CAAAyE,aAAA,CAACkF,aAAa,EAAA;EACZxC,QAAAA,KAAK,EAAEA,KAAM;EACbqC,QAAAA,YAAY,EAAE;YACZhJ,MAAM;YACNC,OAAO;YACPC,WAAW,EAAE2E,eAAe,IAAI,IAAA;WAChC;EACFoE,QAAAA,QAAQ,EAAEA,QAAAA;EAAS,OACpB,CAAC,CAAA;OAEL,CAAA;EACD;EACA;EACA;MACA,OAAOpE,eAAe,KACnB8B,KAAK,CAACvB,KAAK,CAACmE,aAAa,IAAI5C,KAAK,CAACvB,KAAK,CAACkE,YAAY,IAAIwB,KAAK,KAAK,CAAC,CAAC,gBACtEtL,gBAAA,CAAAyE,aAAA,CAACsE,mBAAmB,EAAA;QAClBlH,QAAQ,EAAEwD,eAAe,CAACxD,QAAS;QACnCqH,YAAY,EAAE7D,eAAe,CAAC6D,YAAa;EAC3CQ,MAAAA,SAAS,EAAEI,YAAa;EACxBlC,MAAAA,KAAK,EAAEA,KAAM;QACb6B,QAAQ,EAAEgC,WAAW,EAAG;EACxBjC,MAAAA,YAAY,EAAE;EAAEhJ,QAAAA,MAAM,EAAE,IAAI;UAAEC,OAAO;EAAEC,QAAAA,WAAW,EAAE,IAAA;EAAK,OAAA;EAAE,KAC5D,CAAC,GAEF+K,WAAW,EACZ,CAAA;KACF,EAAE,IAAiC,CAAC,CAAA;EACvC,CAAA;EAAC,IAEIC,cAAc,0BAAdA,cAAc,EAAA;IAAdA,cAAc,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;IAAdA,cAAc,CAAA,gBAAA,CAAA,GAAA,gBAAA,CAAA;IAAdA,cAAc,CAAA,mBAAA,CAAA,GAAA,aAAA,CAAA;EAAA,EAAA,OAAdA,cAAc,CAAA;EAAA,CAAA,CAAdA,cAAc,IAAA,EAAA,CAAA,CAAA;EAAA,IAMdC,mBAAmB,0BAAnBA,mBAAmB,EAAA;IAAnBA,mBAAmB,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;IAAnBA,mBAAmB,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;IAAnBA,mBAAmB,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;IAAnBA,mBAAmB,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;IAAnBA,mBAAmB,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;IAAnBA,mBAAmB,CAAA,oBAAA,CAAA,GAAA,oBAAA,CAAA;IAAnBA,mBAAmB,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;IAAnBA,mBAAmB,CAAA,gBAAA,CAAA,GAAA,gBAAA,CAAA;IAAnBA,mBAAmB,CAAA,mBAAA,CAAA,GAAA,aAAA,CAAA;IAAnBA,mBAAmB,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;EAAA,EAAA,OAAnBA,mBAAmB,CAAA;EAAA,CAAA,CAAnBA,mBAAmB,IAAA,EAAA,CAAA,CAAA;EAaxB,SAASC,yBAAyBA,CAChCC,QAA8C,EAC9C;EACA,EAAA,OAAUA,QAAQ,GAAA,+FAAA,CAAA;EACpB,CAAA;EAEA,SAASC,oBAAoBA,CAACD,QAAwB,EAAE;EACtD,EAAA,IAAIE,GAAG,GAAG/L,gBAAK,CAACoB,UAAU,CAACrB,iBAAiB,CAAC,CAAA;EAC7C,EAAA,CAAUgM,GAAG,GAAb9K,uBAAS,QAAM2K,yBAAyB,CAACC,QAAQ,CAAC,EAAzC,GAAA,KAAA,CAAA,CAAA;EACT,EAAA,OAAOE,GAAG,CAAA;EACZ,CAAA;EAEA,SAASC,kBAAkBA,CAACH,QAA6B,EAAE;EACzD,EAAA,IAAIzH,KAAK,GAAGpE,gBAAK,CAACoB,UAAU,CAACjB,sBAAsB,CAAC,CAAA;EACpD,EAAA,CAAUiE,KAAK,GAAfnD,uBAAS,QAAQ2K,yBAAyB,CAACC,QAAQ,CAAC,EAA3C,GAAA,KAAA,CAAA,CAAA;EACT,EAAA,OAAOzH,KAAK,CAAA;EACd,CAAA;EAEA,SAAS6H,eAAeA,CAACJ,QAA6B,EAAE;EACtD,EAAA,IAAIjG,KAAK,GAAG5F,gBAAK,CAACoB,UAAU,CAACb,YAAY,CAAC,CAAA;EAC1C,EAAA,CAAUqF,KAAK,GAAf3E,uBAAS,QAAQ2K,yBAAyB,CAACC,QAAQ,CAAC,EAA3C,GAAA,KAAA,CAAA,CAAA;EACT,EAAA,OAAOjG,KAAK,CAAA;EACd,CAAA;;EAEA;EACA,SAASsG,iBAAiBA,CAACL,QAA6B,EAAE;EACxD,EAAA,IAAIjG,KAAK,GAAGqG,eAAe,CAACJ,QAAQ,CAAC,CAAA;EACrC,EAAA,IAAIM,SAAS,GAAGvG,KAAK,CAACnF,OAAO,CAACmF,KAAK,CAACnF,OAAO,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAA;EACvD,EAAA,CACEqH,SAAS,CAACvG,KAAK,CAACqE,EAAE,GADpBhJ,uBAAS,CAEJ4K,KAAAA,EAAAA,QAAQ,+DAFJ,GAAA,KAAA,CAAA,CAAA;EAIT,EAAA,OAAOM,SAAS,CAACvG,KAAK,CAACqE,EAAE,CAAA;EAC3B,CAAA;;EAEA;EACA;EACA;EACO,SAASmC,UAAUA,GAAG;EAC3B,EAAA,OAAOF,iBAAiB,CAACP,mBAAmB,CAACU,UAAU,CAAC,CAAA;EAC1D,CAAA;;EAEA;EACA;EACA;EACA;EACO,SAASC,aAAaA,GAAG;EAC9B,EAAA,IAAIlI,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAACY,aAAa,CAAC,CAAA;IACjE,OAAOnI,KAAK,CAACoI,UAAU,CAAA;EACzB,CAAA;;EAEA;EACA;EACA;EACA;EACO,SAASC,cAAcA,GAAG;EAC/B,EAAA,IAAI3J,iBAAiB,GAAGgJ,oBAAoB,CAACJ,cAAc,CAACgB,cAAc,CAAC,CAAA;EAC3E,EAAA,IAAItI,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAACe,cAAc,CAAC,CAAA;EAClE,EAAA,OAAO1M,gBAAK,CAACkC,OAAO,CAClB,OAAO;EACLyK,IAAAA,UAAU,EAAE7J,iBAAiB,CAAC8J,MAAM,CAACD,UAAU;MAC/CvI,KAAK,EAAEA,KAAK,CAAC8E,YAAAA;EACf,GAAC,CAAC,EACF,CAACpG,iBAAiB,CAAC8J,MAAM,CAACD,UAAU,EAAEvI,KAAK,CAAC8E,YAAY,CAC1D,CAAC,CAAA;EACH,CAAA;;EAEA;EACA;EACA;EACA;EACO,SAAS2D,UAAUA,GAAc;IACtC,IAAI;MAAEpM,OAAO;EAAEyK,IAAAA,UAAAA;EAAW,GAAC,GAAGc,kBAAkB,CAC9CL,mBAAmB,CAACmB,UACtB,CAAC,CAAA;IACD,OAAO9M,gBAAK,CAACkC,OAAO,CAClB,MAAMzB,OAAO,CAACyG,GAAG,CAAEuD,CAAC,IAAKsC,wCAA0B,CAACtC,CAAC,EAAES,UAAU,CAAC,CAAC,EACnE,CAACzK,OAAO,EAAEyK,UAAU,CACtB,CAAC,CAAA;EACH,CAAA;;EAEA;EACA;EACA;EACO,SAAS8B,aAAaA,GAAY;EACvC,EAAA,IAAI5I,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAACsB,aAAa,CAAC,CAAA;EACjE,EAAA,IAAIC,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAACsB,aAAa,CAAC,CAAA;EAElE,EAAA,IAAI7I,KAAK,CAACgG,MAAM,IAAIhG,KAAK,CAACgG,MAAM,CAAC8C,OAAO,CAAC,IAAI,IAAI,EAAE;EACjDxE,IAAAA,OAAO,CAACd,KAAK,CACkDsF,0DAAAA,GAAAA,OAAO,MACtE,CAAC,CAAA;EACD,IAAA,OAAOrG,SAAS,CAAA;EAClB,GAAA;EACA,EAAA,OAAOzC,KAAK,CAAC8G,UAAU,CAACgC,OAAO,CAAC,CAAA;EAClC,CAAA;;EAEA;EACA;EACA;EACO,SAASC,kBAAkBA,CAACD,OAAe,EAAW;EAC3D,EAAA,IAAI9I,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAACyB,kBAAkB,CAAC,CAAA;EACtE,EAAA,OAAOhJ,KAAK,CAAC8G,UAAU,CAACgC,OAAO,CAAC,CAAA;EAClC,CAAA;;EAEA;EACA;EACA;EACO,SAASG,aAAaA,GAAY;EACvC,EAAA,IAAIjJ,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAAC2B,aAAa,CAAC,CAAA;EACjE,EAAA,IAAIJ,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAACsB,aAAa,CAAC,CAAA;IAClE,OAAO7I,KAAK,CAACmJ,UAAU,GAAGnJ,KAAK,CAACmJ,UAAU,CAACL,OAAO,CAAC,GAAGrG,SAAS,CAAA;EACjE,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACO,SAASgB,aAAaA,GAAY;EAAA,EAAA,IAAA2F,aAAA,CAAA;EACvC,EAAA,IAAI5F,KAAK,GAAG5H,gBAAK,CAACoB,UAAU,CAACT,iBAAiB,CAAC,CAAA;EAC/C,EAAA,IAAIyD,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAAC8B,aAAa,CAAC,CAAA;EACjE,EAAA,IAAIP,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAAC8B,aAAa,CAAC,CAAA;;EAElE;EACA;IACA,IAAI7F,KAAK,KAAKf,SAAS,EAAE;EACvB,IAAA,OAAOe,KAAK,CAAA;EACd,GAAA;;EAEA;IACA,OAAA4F,CAAAA,aAAA,GAAOpJ,KAAK,CAACgG,MAAM,KAAZoD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,aAAA,CAAeN,OAAO,CAAC,CAAA;EAChC,CAAA;;EAEA;EACA;EACA;EACO,SAASQ,aAAaA,GAAY;EACvC,EAAA,IAAI/I,KAAK,GAAG3E,gBAAK,CAACoB,UAAU,CAAChB,YAAY,CAAC,CAAA;EAC1C,EAAA,OAAOuE,KAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAEgJ,KAAK,CAAA;EACrB,CAAA;;EAEA;EACA;EACA;EACO,SAASC,aAAaA,GAAY;EACvC,EAAA,IAAIjJ,KAAK,GAAG3E,gBAAK,CAACoB,UAAU,CAAChB,YAAY,CAAC,CAAA;EAC1C,EAAA,OAAOuE,KAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAEkJ,MAAM,CAAA;EACtB,CAAA;EAEA,IAAIC,SAAS,GAAG,CAAC,CAAA;;EAEjB;EACA;EACA;EACA;EACA;EACA;EACO,SAASC,UAAUA,CAACC,WAAsC,EAAW;IAC1E,IAAI;cAAEpB,QAAM;EAAE1L,IAAAA,QAAAA;EAAS,GAAC,GAAG4K,oBAAoB,CAACJ,cAAc,CAACuC,UAAU,CAAC,CAAA;EAC1E,EAAA,IAAI7J,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAACsC,UAAU,CAAC,CAAA;IAE9D,IAAI,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnO,gBAAK,CAACoO,QAAQ,CAAC,EAAE,CAAC,CAAA;EACpD,EAAA,IAAIC,eAAe,GAAGrO,gBAAK,CAAC0D,WAAW,CACpC4K,GAAG,IAAK;EACP,IAAA,IAAI,OAAON,WAAW,KAAK,UAAU,EAAE;QACrC,OAAO,CAAC,CAACA,WAAW,CAAA;EACtB,KAAA;MACA,IAAI9M,QAAQ,KAAK,GAAG,EAAE;QACpB,OAAO8M,WAAW,CAACM,GAAG,CAAC,CAAA;EACzB,KAAA;;EAEA;EACA;EACA;MACA,IAAI;QAAEC,eAAe;QAAEC,YAAY;EAAEC,MAAAA,aAAAA;EAAc,KAAC,GAAGH,GAAG,CAAA;EAC1D,IAAA,OAAON,WAAW,CAAC;QACjBO,eAAe,EAAAhH,QAAA,CAAA,EAAA,EACVgH,eAAe,EAAA;UAClBjN,QAAQ,EACNoN,oBAAa,CAACH,eAAe,CAACjN,QAAQ,EAAEJ,QAAQ,CAAC,IACjDqN,eAAe,CAACjN,QAAAA;SACnB,CAAA;QACDkN,YAAY,EAAAjH,QAAA,CAAA,EAAA,EACPiH,YAAY,EAAA;UACflN,QAAQ,EACNoN,oBAAa,CAACF,YAAY,CAAClN,QAAQ,EAAEJ,QAAQ,CAAC,IAC9CsN,YAAY,CAAClN,QAAAA;SAChB,CAAA;EACDmN,MAAAA,aAAAA;EACF,KAAC,CAAC,CAAA;EACJ,GAAC,EACD,CAACvN,QAAQ,EAAE8M,WAAW,CACxB,CAAC,CAAA;;EAED;EACA;IACAhO,gBAAK,CAAC2O,SAAS,CAAC,MAAM;EACpB,IAAA,IAAInH,GAAG,GAAGoH,MAAM,CAAC,EAAEd,SAAS,CAAC,CAAA;MAC7BK,aAAa,CAAC3G,GAAG,CAAC,CAAA;EAClB,IAAA,OAAO,MAAMoF,QAAM,CAACiC,aAAa,CAACrH,GAAG,CAAC,CAAA;EACxC,GAAC,EAAE,CAACoF,QAAM,CAAC,CAAC,CAAA;;EAEZ;EACA;EACA;EACA;IACA5M,gBAAK,CAAC2O,SAAS,CAAC,MAAM;MACpB,IAAIT,UAAU,KAAK,EAAE,EAAE;EACrBtB,MAAAA,QAAM,CAACkC,UAAU,CAACZ,UAAU,EAAEG,eAAe,CAAC,CAAA;EAChD,KAAA;KACD,EAAE,CAACzB,QAAM,EAAEsB,UAAU,EAAEG,eAAe,CAAC,CAAC,CAAA;;EAEzC;EACA;IACA,OAAOH,UAAU,IAAI9J,KAAK,CAAC2K,QAAQ,CAACC,GAAG,CAACd,UAAU,CAAC,GAC/C9J,KAAK,CAAC2K,QAAQ,CAACE,GAAG,CAACf,UAAU,CAAC,GAC9BgB,mBAAY,CAAA;EAClB,CAAA;;EAEA;EACA;EACA;EACA;EACA,SAAStM,iBAAiBA,GAAqB;IAC7C,IAAI;EAAEgK,YAAAA,QAAAA;EAAO,GAAC,GAAGd,oBAAoB,CAACJ,cAAc,CAACyD,iBAAiB,CAAC,CAAA;EACvE,EAAA,IAAIlF,EAAE,GAAGiC,iBAAiB,CAACP,mBAAmB,CAACwD,iBAAiB,CAAC,CAAA;EAEjE,EAAA,IAAI7L,SAAS,GAAGtD,gBAAK,CAACuD,MAAM,CAAC,KAAK,CAAC,CAAA;EACnCjB,EAAAA,yBAAyB,CAAC,MAAM;MAC9BgB,SAAS,CAACE,OAAO,GAAG,IAAI,CAAA;EAC1B,GAAC,CAAC,CAAA;IAEF,IAAIC,QAA0B,GAAGzD,gBAAK,CAAC0D,WAAW,CAChD,UAAC7C,EAAe,EAAE8C,OAAwB,EAAU;EAAA,IAAA,IAAlCA,OAAwB,KAAA,KAAA,CAAA,EAAA;QAAxBA,OAAwB,GAAG,EAAE,CAAA;EAAA,KAAA;EAC7CC,IAAAC,qBAAO,CAACP,SAAS,CAACE,OAAO,EAAEnB,qBAAqB,CAAC,CAAA,CAAA;;EAEjD;EACA;EACA,IAAA,IAAI,CAACiB,SAAS,CAACE,OAAO,EAAE,OAAA;EAExB,IAAA,IAAI,OAAO3C,EAAE,KAAK,QAAQ,EAAE;EAC1B+L,MAAAA,QAAM,CAACnJ,QAAQ,CAAC5C,EAAE,CAAC,CAAA;EACrB,KAAC,MAAM;EACL+L,MAAAA,QAAM,CAACnJ,QAAQ,CAAC5C,EAAE,EAAA0G,QAAA,CAAA;EAAI6H,QAAAA,WAAW,EAAEnF,EAAAA;SAAOtG,EAAAA,OAAO,CAAE,CAAC,CAAA;EACtD,KAAA;EACF,GAAC,EACD,CAACiJ,QAAM,EAAE3C,EAAE,CACb,CAAC,CAAA;EAED,EAAA,OAAOxG,QAAQ,CAAA;EACjB,CAAA;EAEA,MAAM4L,eAAsC,GAAG,EAAE,CAAA;EAEjD,SAASvJ,WAAWA,CAAC0B,GAAW,EAAE8H,IAAa,EAAExH,OAAe,EAAE;IAChE,IAAI,CAACwH,IAAI,IAAI,CAACD,eAAa,CAAC7H,GAAG,CAAC,EAAE;EAChC6H,IAAAA,eAAa,CAAC7H,GAAG,CAAC,GAAG,IAAI,CAAA;MACzB3D,qBAAO,CAAC,KAAK,EAAEiE,OAAO,CAAC,CAAA,CAAA;EACzB,GAAA;EACF;;EC9lCA,MAAMuH,aAAyC,GAAG,EAAE,CAAA;EAE7C,SAASE,QAAQA,CAAC/H,GAAW,EAAEM,OAAe,EAAQ;IAC3D,IAAe,CAACuH,aAAa,CAACvH,OAAO,CAAC,EAAE;EACtCuH,IAAAA,aAAa,CAACvH,OAAO,CAAC,GAAG,IAAI,CAAA;EAC7BY,IAAAA,OAAO,CAAC8G,IAAI,CAAC1H,OAAO,CAAC,CAAA;EACvB,GAAA;EACF,CAAA;EAEA,MAAM2H,cAAc,GAAGA,CAACC,IAAY,EAAEC,GAAW,EAAEC,IAAY,KAC7DL,QAAQ,CACNG,IAAI,EACJ,oDAAwCC,GAAG,GAAA,IAAA,IAAA,mBAAA,GACpBD,IAAI,GAAkC,iCAAA,CAAA,IAAA,4BAAA,GAC9BE,IAAI,GAAA,GAAA,CACrC,CAAC,CAAA;EAEI,SAASC,wBAAwBA,CACtCC,YAAqD,EACrDC,YAA6D,EAC7D;IACA,IAAI,CAAAD,YAAY,IAAZA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,YAAY,CAAEE,kBAAkB,MAAKnJ,SAAS,EAAE;EAClD4I,IAAAA,cAAc,CACZ,oBAAoB,EACpB,iFAAiF,EACjF,gEACF,CAAC,CAAA;EACH,GAAA;EAEA,EAAA,IACE,CAAAK,YAAY,IAAA,IAAA,GAAA,KAAA,CAAA,GAAZA,YAAY,CAAEzM,oBAAoB,MAAKwD,SAAS,KAC/C,CAACkJ,YAAY,IAAIA,YAAY,CAAC1M,oBAAoB,KAAKwD,SAAS,CAAC,EAClE;EACA4I,IAAAA,cAAc,CACZ,sBAAsB,EACtB,iEAAiE,EACjE,kEACF,CAAC,CAAA;EACH,GAAA;EAEA,EAAA,IAAIM,YAAY,EAAE;EAChB,IAAA,IAAIA,YAAY,CAACE,iBAAiB,KAAKpJ,SAAS,EAAE;EAChD4I,MAAAA,cAAc,CACZ,mBAAmB,EACnB,wDAAwD,EACxD,+DACF,CAAC,CAAA;EACH,KAAA;EAEA,IAAA,IAAIM,YAAY,CAACG,sBAAsB,KAAKrJ,SAAS,EAAE;EACrD4I,MAAAA,cAAc,CACZ,wBAAwB,EACxB,sEAAsE,EACtE,oEACF,CAAC,CAAA;EACH,KAAA;EAEA,IAAA,IAAIM,YAAY,CAAC1F,mBAAmB,KAAKxD,SAAS,EAAE;EAClD4I,MAAAA,cAAc,CACZ,qBAAqB,EACrB,uDAAuD,EACvD,iEACF,CAAC,CAAA;EACH,KAAA;EAEA,IAAA,IAAIM,YAAY,CAACI,8BAA8B,KAAKtJ,SAAS,EAAE;EAC7D4I,MAAAA,cAAc,CACZ,gCAAgC,EAChC,8EAA8E,EAC9E,4EACF,CAAC,CAAA;EACH,KAAA;EACF,GAAA;EACF;;ECVA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA,MAAMW,gBAAgB,GAAG,iBAAiB,CAAA;EAC1C,MAAMC,mBAAmB,GAAGrQ,gBAAK,CAACoQ,gBAAgB,CAAC,CAAA;;EAEnD;EACA;EACA;EACO,SAASE,cAAcA,CAAA1G,IAAA,EAIc;IAAA,IAJb;MAC7B2G,eAAe;cACf3D,QAAM;EACN7J,IAAAA,MAAAA;EACmB,GAAC,GAAA6G,IAAA,CAAA;EACpB,EAAA,IAAI,CAACxF,KAAK,EAAEoM,YAAY,CAAC,GAAGxQ,gBAAK,CAACoO,QAAQ,CAACxB,QAAM,CAACxI,KAAK,CAAC,CAAA;IACxD,IAAI;EAAE4L,IAAAA,kBAAAA;EAAmB,GAAC,GAAGjN,MAAM,IAAI,EAAE,CAAA;EAEzC,EAAA,IAAI0N,QAAQ,GAAGzQ,gBAAK,CAAC0D,WAAW,CAC7BgN,QAAqB,IAAK;MACzB,IAAIV,kBAAkB,IAAIK,mBAAmB,EAAE;EAC7CA,MAAAA,mBAAmB,CAAC,MAAMG,YAAY,CAACE,QAAQ,CAAC,CAAC,CAAA;EACnD,KAAC,MAAM;QACLF,YAAY,CAACE,QAAQ,CAAC,CAAA;EACxB,KAAA;EACF,GAAC,EACD,CAACF,YAAY,EAAER,kBAAkB,CACnC,CAAC,CAAA;;EAED;EACA;EACAhQ,EAAAA,gBAAK,CAAC0C,eAAe,CAAC,MAAMkK,QAAM,CAAC+D,SAAS,CAACF,QAAQ,CAAC,EAAE,CAAC7D,QAAM,EAAE6D,QAAQ,CAAC,CAAC,CAAA;IAE3EzQ,gBAAK,CAAC2O,SAAS,CAAC,MAAM;MACpB9K,qBAAO,CACL0M,eAAe,IAAI,IAAI,IAAI,CAAC3D,QAAM,CAAC7J,MAAM,CAACsH,mBAAmB,EAC7D,8DAA8D,GAC5D,kEACJ,CAAC,CAAA,CAAA;EACD;EACA;KACD,EAAE,EAAE,CAAC,CAAA;EAEN,EAAA,IAAIlJ,SAAS,GAAGnB,gBAAK,CAACkC,OAAO,CAAC,MAAiB;MAC7C,OAAO;QACLP,UAAU,EAAEiL,QAAM,CAACjL,UAAU;QAC7B2F,cAAc,EAAEsF,QAAM,CAACtF,cAAc;QACrCxD,EAAE,EAAG8M,CAAC,IAAKhE,QAAM,CAACnJ,QAAQ,CAACmN,CAAC,CAAC;EAC7BzM,MAAAA,IAAI,EAAEA,CAACtD,EAAE,EAAEuD,KAAK,EAAEyM,IAAI,KACpBjE,QAAM,CAACnJ,QAAQ,CAAC5C,EAAE,EAAE;UAClBuD,KAAK;EACL0M,QAAAA,kBAAkB,EAAED,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEC,kBAAAA;EAC5B,OAAC,CAAC;EACJ5M,MAAAA,OAAO,EAAEA,CAACrD,EAAE,EAAEuD,KAAK,EAAEyM,IAAI,KACvBjE,QAAM,CAACnJ,QAAQ,CAAC5C,EAAE,EAAE;EAClBqD,QAAAA,OAAO,EAAE,IAAI;UACbE,KAAK;EACL0M,QAAAA,kBAAkB,EAAED,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEC,kBAAAA;SAC3B,CAAA;OACJ,CAAA;EACH,GAAC,EAAE,CAAClE,QAAM,CAAC,CAAC,CAAA;EAEZ,EAAA,IAAI1L,QAAQ,GAAG0L,QAAM,CAAC1L,QAAQ,IAAI,GAAG,CAAA;EAErC,EAAA,IAAI4B,iBAAiB,GAAG9C,gBAAK,CAACkC,OAAO,CACnC,OAAO;cACL0K,QAAM;MACNzL,SAAS;EACTsB,IAAAA,MAAM,EAAE,KAAK;EACbvB,IAAAA,QAAAA;KACD,CAAC,EACF,CAAC0L,QAAM,EAAEzL,SAAS,EAAED,QAAQ,CAC9B,CAAC,CAAA;EAEDlB,EAAAA,gBAAK,CAAC2O,SAAS,CACb,MAAMkB,wBAAwB,CAAC9M,MAAM,EAAE6J,QAAM,CAAC7J,MAAM,CAAC,EACrD,CAAC6J,QAAM,EAAE7J,MAAM,CACjB,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA,EAAA,oBACE/C,gBAAA,CAAAyE,aAAA,CAAAzE,gBAAA,CAAA2I,QAAA,EACE3I,IAAAA,eAAAA,gBAAA,CAAAyE,aAAA,CAAC1E,iBAAiB,CAAC2E,QAAQ,EAAA;EAACC,IAAAA,KAAK,EAAE7B,iBAAAA;EAAkB,GAAA,eACnD9C,gBAAA,CAAAyE,aAAA,CAACtE,sBAAsB,CAACuE,QAAQ,EAAA;EAACC,IAAAA,KAAK,EAAEP,KAAAA;EAAM,GAAA,eAC5CpE,gBAAA,CAAAyE,aAAA,CAACsM,MAAM,EAAA;EACL7P,IAAAA,QAAQ,EAAEA,QAAS;MACnBW,QAAQ,EAAEuC,KAAK,CAACvC,QAAS;MACzBE,cAAc,EAAEqC,KAAK,CAACqK,aAAc;EACpCtN,IAAAA,SAAS,EAAEA,SAAU;EACrB4B,IAAAA,MAAM,EAAE;EACNM,MAAAA,oBAAoB,EAAEuJ,QAAM,CAAC7J,MAAM,CAACM,oBAAAA;EACtC,KAAA;EAAE,GAAA,EAEDe,KAAK,CAACkG,WAAW,IAAIsC,QAAM,CAAC7J,MAAM,CAACsH,mBAAmB,gBACrDrK,gBAAA,CAAAyE,aAAA,CAACuM,UAAU,EAAA;MACT9L,MAAM,EAAE0H,QAAM,CAAC1H,MAAO;MACtBnC,MAAM,EAAE6J,QAAM,CAAC7J,MAAO;EACtBqB,IAAAA,KAAK,EAAEA,KAAAA;KACR,CAAC,GAEFmM,eAEI,CACuB,CACP,CAAC,EAC5B,IACD,CAAC,CAAA;EAEP,CAAA;EAEA,SAASS,UAAUA,CAAAC,KAAA,EAQW;IAAA,IARV;MAClB/L,MAAM;MACNnC,MAAM;EACNqB,IAAAA,KAAAA;EAKF,GAAC,GAAA6M,KAAA,CAAA;IACC,OAAO7L,aAAa,CAACF,MAAM,EAAE2B,SAAS,EAAEzC,KAAK,EAAErB,MAAM,CAAC,CAAA;EACxD,CAAA;EAUA;EACA;EACA;EACA;EACA;EACO,SAASmO,YAAYA,CAAAC,KAAA,EAMc;IAAA,IANb;MAC3BjQ,QAAQ;MACRuI,QAAQ;MACR2H,cAAc;MACdC,YAAY;EACZtO,IAAAA,MAAAA;EACiB,GAAC,GAAAoO,KAAA,CAAA;EAClB,EAAA,IAAIG,UAAU,GAAGtR,gBAAK,CAACuD,MAAM,EAAiB,CAAA;EAC9C,EAAA,IAAI+N,UAAU,CAAC9N,OAAO,IAAI,IAAI,EAAE;EAC9B8N,IAAAA,UAAU,CAAC9N,OAAO,GAAG+N,0BAAmB,CAAC;QACvCH,cAAc;QACdC,YAAY;EACZG,MAAAA,QAAQ,EAAE,IAAA;EACZ,KAAC,CAAC,CAAA;EACJ,GAAA;EAEA,EAAA,IAAIC,OAAO,GAAGH,UAAU,CAAC9N,OAAO,CAAA;IAChC,IAAI,CAACY,KAAK,EAAEoM,YAAY,CAAC,GAAGxQ,gBAAK,CAACoO,QAAQ,CAAC;MACzCsD,MAAM,EAAED,OAAO,CAACC,MAAM;MACtB7P,QAAQ,EAAE4P,OAAO,CAAC5P,QAAAA;EACpB,GAAC,CAAC,CAAA;IACF,IAAI;EAAEmO,IAAAA,kBAAAA;EAAmB,GAAC,GAAGjN,MAAM,IAAI,EAAE,CAAA;EACzC,EAAA,IAAI0N,QAAQ,GAAGzQ,gBAAK,CAAC0D,WAAW,CAC7BgN,QAAwD,IAAK;EAC5DV,IAAAA,kBAAkB,IAAIK,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMG,YAAY,CAACE,QAAQ,CAAC,CAAC,GACjDF,YAAY,CAACE,QAAQ,CAAC,CAAA;EAC5B,GAAC,EACD,CAACF,YAAY,EAAER,kBAAkB,CACnC,CAAC,CAAA;EAEDhQ,EAAAA,gBAAK,CAAC0C,eAAe,CAAC,MAAM+O,OAAO,CAACE,MAAM,CAAClB,QAAQ,CAAC,EAAE,CAACgB,OAAO,EAAEhB,QAAQ,CAAC,CAAC,CAAA;EAE1EzQ,EAAAA,gBAAK,CAAC2O,SAAS,CAAC,MAAMkB,wBAAwB,CAAC9M,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAA;EAEjE,EAAA,oBACE/C,gBAAA,CAAAyE,aAAA,CAACsM,MAAM,EAAA;EACL7P,IAAAA,QAAQ,EAAEA,QAAS;EACnBuI,IAAAA,QAAQ,EAAEA,QAAS;MACnB5H,QAAQ,EAAEuC,KAAK,CAACvC,QAAS;MACzBE,cAAc,EAAEqC,KAAK,CAACsN,MAAO;EAC7BvQ,IAAAA,SAAS,EAAEsQ,OAAQ;EACnB1O,IAAAA,MAAM,EAAEA,MAAAA;EAAO,GAChB,CAAC,CAAA;EAEN,CAAA;EASA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS6O,QAAQA,CAAAC,KAAA,EAKA;IAAA,IALC;MACvBhR,EAAE;MACFqD,OAAO;MACPE,KAAK;EACLrD,IAAAA,QAAAA;EACa,GAAC,GAAA8Q,KAAA,CAAA;IACd,CACE7Q,kBAAkB,EAAE,GADtBC,uBAAS,CAEP,KAAA;EACA;EAAA,EAAA,qEAAA,CAAA,CAHO,GAAA,KAAA,CAAA,CAAA;IAOT,IAAI;MAAE8B,MAAM;EAAEN,IAAAA,MAAM,EAAED,QAAAA;EAAS,GAAC,GAAGxC,gBAAK,CAACoB,UAAU,CAACf,iBAAiB,CAAC,CAAA;EAEtEuD,EAAAC,qBAAO,CACL,CAACrB,QAAQ,EACT,yEAC0E,GAAA,wEAAA,GAAA,0EAE5E,CAAC,CAAA,CAAA;IAED,IAAI;EAAE/B,IAAAA,OAAAA;EAAQ,GAAC,GAAGT,gBAAK,CAACoB,UAAU,CAACb,YAAY,CAAC,CAAA;IAChD,IAAI;EAAEe,IAAAA,QAAQ,EAAE0B,gBAAAA;KAAkB,GAAGpB,WAAW,EAAE,CAAA;EAClD,EAAA,IAAI6B,QAAQ,GAAGd,WAAW,EAAE,CAAA;;EAE5B;EACA;IACA,IAAIoB,IAAI,GAAGC,gBAAS,CAClBnD,EAAE,EACFuC,iCAAmB,CAAC3C,OAAO,EAAEsC,MAAM,CAACM,oBAAoB,CAAC,EACzDL,gBAAgB,EAChBjC,QAAQ,KAAK,MACf,CAAC,CAAA;EACD,EAAA,IAAI+Q,QAAQ,GAAG5O,IAAI,CAACC,SAAS,CAACY,IAAI,CAAC,CAAA;EAEnC/D,EAAAA,gBAAK,CAAC2O,SAAS,CACb,MAAMlL,QAAQ,CAACP,IAAI,CAACe,KAAK,CAAC6N,QAAQ,CAAC,EAAE;MAAE5N,OAAO;MAAEE,KAAK;EAAErD,IAAAA,QAAAA;EAAS,GAAC,CAAC,EAClE,CAAC0C,QAAQ,EAAEqO,QAAQ,EAAE/Q,QAAQ,EAAEmD,OAAO,EAAEE,KAAK,CAC/C,CAAC,CAAA;EAED,EAAA,OAAO,IAAI,CAAA;EACb,CAAA;EAMA;EACA;EACA;EACA;EACA;EACO,SAAS2N,MAAMA,CAAC9I,KAAkB,EAA6B;EACpE,EAAA,OAAO1E,SAAS,CAAC0E,KAAK,CAACzE,OAAO,CAAC,CAAA;EACjC,CAAA;EA8CA;EACA;EACA;EACA;EACA;EACO,SAASwN,KAAKA,CAACC,MAAkB,EAA6B;EACnE,EAAAhR,uBAAS,CAAA,KAAA,EAEP,sEACoE,GAAA,kEAAA,CAAA,CAH7D,CAAA,CAAA;EAKX,CAAA;EAYA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS8P,MAAMA,CAAAmB,KAAA,EAQqB;IAAA,IARpB;MACrBhR,QAAQ,EAAEiR,YAAY,GAAG,GAAG;EAC5B1I,IAAAA,QAAQ,GAAG,IAAI;EACf5H,IAAAA,QAAQ,EAAEuQ,YAAY;MACtBrQ,cAAc,GAAG0F,aAAc,CAACC,GAAG;MACnCvG,SAAS;MACTsB,MAAM,EAAE4P,UAAU,GAAG,KAAK;EAC1BtP,IAAAA,MAAAA;EACW,GAAC,GAAAmP,KAAA,CAAA;EACZ,EAAA,CACE,CAAClR,kBAAkB,EAAE,GADvBC,uBAAS,CAEP,KAAA,EAAA,uDAAA,GAAA,mDACqD,EAH9C,GAAA,KAAA,CAAA,CAAA;;EAMT;EACA;IACA,IAAIC,QAAQ,GAAGiR,YAAY,CAACjO,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;EAChD,EAAA,IAAIoO,iBAAiB,GAAGtS,gBAAK,CAACkC,OAAO,CACnC,OAAO;MACLhB,QAAQ;MACRC,SAAS;EACTsB,IAAAA,MAAM,EAAE4P,UAAU;EAClBtP,IAAAA,MAAM,EAAAwE,QAAA,CAAA;EACJlE,MAAAA,oBAAoB,EAAE,KAAA;EAAK,KAAA,EACxBN,MAAM,CAAA;KAEZ,CAAC,EACF,CAAC7B,QAAQ,EAAE6B,MAAM,EAAE5B,SAAS,EAAEkR,UAAU,CAC1C,CAAC,CAAA;EAED,EAAA,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;EACpCA,IAAAA,YAAY,GAAGjM,gBAAS,CAACiM,YAAY,CAAC,CAAA;EACxC,GAAA;IAEA,IAAI;EACF9Q,IAAAA,QAAQ,GAAG,GAAG;EACdC,IAAAA,MAAM,GAAG,EAAE;EACXF,IAAAA,IAAI,GAAG,EAAE;EACT+C,IAAAA,KAAK,GAAG,IAAI;EACZoD,IAAAA,GAAG,GAAG,SAAA;EACR,GAAC,GAAG4K,YAAY,CAAA;EAEhB,EAAA,IAAIG,eAAe,GAAGvS,gBAAK,CAACkC,OAAO,CAAC,MAAM;EACxC,IAAA,IAAIsQ,gBAAgB,GAAG9D,oBAAa,CAACpN,QAAQ,EAAEJ,QAAQ,CAAC,CAAA;MAExD,IAAIsR,gBAAgB,IAAI,IAAI,EAAE;EAC5B,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;MAEA,OAAO;EACL3Q,MAAAA,QAAQ,EAAE;EACRP,QAAAA,QAAQ,EAAEkR,gBAAgB;UAC1BjR,MAAM;UACNF,IAAI;UACJ+C,KAAK;EACLoD,QAAAA,GAAAA;SACD;EACDzF,MAAAA,cAAAA;OACD,CAAA;EACH,GAAC,EAAE,CAACb,QAAQ,EAAEI,QAAQ,EAAEC,MAAM,EAAEF,IAAI,EAAE+C,KAAK,EAAEoD,GAAG,EAAEzF,cAAc,CAAC,CAAC,CAAA;IAElE8B,qBAAO,CACL0O,eAAe,IAAI,IAAI,EACvB,qBAAqBrR,GAAAA,QAAQ,iDACvBI,QAAQ,GAAGC,MAAM,GAAGF,IAAI,GAAuC,wCAAA,CAAA,GAAA,kDAEvE,CAAC,CAAA,CAAA;IAED,IAAIkR,eAAe,IAAI,IAAI,EAAE;EAC3B,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;EAEA,EAAA,oBACEvS,gBAAA,CAAAyE,aAAA,CAACpE,iBAAiB,CAACqE,QAAQ,EAAA;EAACC,IAAAA,KAAK,EAAE2N,iBAAAA;EAAkB,GAAA,eACnDtS,gBAAA,CAAAyE,aAAA,CAACnE,eAAe,CAACoE,QAAQ,EAAA;EAAC+E,IAAAA,QAAQ,EAAEA,QAAS;EAAC9E,IAAAA,KAAK,EAAE4N,eAAAA;EAAgB,GAAE,CAC7C,CAAC,CAAA;EAEjC,CAAA;EAOA;EACA;EACA;EACA;EACA;EACA;EACO,SAASE,MAAMA,CAAAC,KAAA,EAGqB;IAAA,IAHpB;MACrBjJ,QAAQ;EACR5H,IAAAA,QAAAA;EACW,GAAC,GAAA6Q,KAAA,CAAA;IACZ,OAAOzN,SAAS,CAAC0N,wBAAwB,CAAClJ,QAAQ,CAAC,EAAE5H,QAAQ,CAAC,CAAA;EAChE,CAAA;EAYA;EACA;EACA;EACA;EACO,SAAS+Q,KAAKA,CAAAC,KAAA,EAAkD;IAAA,IAAjD;MAAEpJ,QAAQ;MAAEK,YAAY;EAAEgJ,IAAAA,OAAAA;EAAoB,GAAC,GAAAD,KAAA,CAAA;EACnE,EAAA,oBACE7S,gBAAA,CAAAyE,aAAA,CAACsO,kBAAkB,EAAA;EAACD,IAAAA,OAAO,EAAEA,OAAQ;EAAChJ,IAAAA,YAAY,EAAEA,YAAAA;KAClD9J,eAAAA,gBAAA,CAAAyE,aAAA,CAACuO,YAAY,EAAEvJ,IAAAA,EAAAA,QAAuB,CACpB,CAAC,CAAA;EAEzB,CAAA;EAAC,IAWIwJ,iBAAiB,0BAAjBA,iBAAiB,EAAA;EAAjBA,EAAAA,iBAAiB,CAAjBA,iBAAiB,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA;EAAjBA,EAAAA,iBAAiB,CAAjBA,iBAAiB,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA;EAAjBA,EAAAA,iBAAiB,CAAjBA,iBAAiB,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAA,CAAA;EAAA,EAAA,OAAjBA,iBAAiB,CAAA;EAAA,CAAA,CAAjBA,iBAAiB,IAAA,EAAA,CAAA,CAAA;EAMtB,MAAMC,mBAAmB,GAAG,IAAIC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;EAEjD,MAAMJ,kBAAkB,SAAS/S,gBAAK,CAAC8G,SAAS,CAG9C;IACAkC,WAAWA,CAACC,KAA8B,EAAE;MAC1C,KAAK,CAACA,KAAK,CAAC,CAAA;MACZ,IAAI,CAAC7E,KAAK,GAAG;EAAEwD,MAAAA,KAAK,EAAE,IAAA;OAAM,CAAA;EAC9B,GAAA;IAEA,OAAOuB,wBAAwBA,CAACvB,KAAU,EAAE;MAC1C,OAAO;EAAEA,MAAAA,KAAAA;OAAO,CAAA;EAClB,GAAA;EAEAyB,EAAAA,iBAAiBA,CAACzB,KAAU,EAAE0B,SAAc,EAAE;MAC5CZ,OAAO,CAACd,KAAK,CACX,kDAAkD,EAClDA,KAAK,EACL0B,SACF,CAAC,CAAA;EACH,GAAA;EAEAC,EAAAA,MAAMA,GAAG;MACP,IAAI;QAAEE,QAAQ;QAAEK,YAAY;EAAEgJ,MAAAA,OAAAA;OAAS,GAAG,IAAI,CAAC7J,KAAK,CAAA;MAEpD,IAAImK,OAA8B,GAAG,IAAI,CAAA;EACzC,IAAA,IAAIpL,MAAyB,GAAGiL,iBAAiB,CAACI,OAAO,CAAA;EAEzD,IAAA,IAAI,EAAEP,OAAO,YAAYK,OAAO,CAAC,EAAE;EACjC;QACAnL,MAAM,GAAGiL,iBAAiB,CAACK,OAAO,CAAA;EAClCF,MAAAA,OAAO,GAAGD,OAAO,CAACL,OAAO,EAAE,CAAA;EAC3B1L,MAAAA,MAAM,CAACmM,cAAc,CAACH,OAAO,EAAE,UAAU,EAAE;UAAEnE,GAAG,EAAEA,MAAM,IAAA;EAAK,OAAC,CAAC,CAAA;EAC/D7H,MAAAA,MAAM,CAACmM,cAAc,CAACH,OAAO,EAAE,OAAO,EAAE;UAAEnE,GAAG,EAAEA,MAAM6D,OAAAA;EAAQ,OAAC,CAAC,CAAA;EACjE,KAAC,MAAM,IAAI,IAAI,CAAC1O,KAAK,CAACwD,KAAK,EAAE;EAC3B;QACAI,MAAM,GAAGiL,iBAAiB,CAACrL,KAAK,CAAA;EAChC,MAAA,IAAI4L,WAAW,GAAG,IAAI,CAACpP,KAAK,CAACwD,KAAK,CAAA;EAClCwL,MAAAA,OAAO,GAAGD,OAAO,CAACM,MAAM,EAAE,CAACC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;EAC3CtM,MAAAA,MAAM,CAACmM,cAAc,CAACH,OAAO,EAAE,UAAU,EAAE;UAAEnE,GAAG,EAAEA,MAAM,IAAA;EAAK,OAAC,CAAC,CAAA;EAC/D7H,MAAAA,MAAM,CAACmM,cAAc,CAACH,OAAO,EAAE,QAAQ,EAAE;UAAEnE,GAAG,EAAEA,MAAMuE,WAAAA;EAAY,OAAC,CAAC,CAAA;EACtE,KAAC,MAAM,IAAKV,OAAO,CAAoBa,QAAQ,EAAE;EAC/C;EACAP,MAAAA,OAAO,GAAGN,OAAO,CAAA;EACjB9K,MAAAA,MAAM,GACJ,QAAQ,IAAIoL,OAAO,GACfH,iBAAiB,CAACrL,KAAK,GACvB,OAAO,IAAIwL,OAAO,GAClBH,iBAAiB,CAACK,OAAO,GACzBL,iBAAiB,CAACI,OAAO,CAAA;EACjC,KAAC,MAAM;EACL;QACArL,MAAM,GAAGiL,iBAAiB,CAACI,OAAO,CAAA;EAClCjM,MAAAA,MAAM,CAACmM,cAAc,CAACT,OAAO,EAAE,UAAU,EAAE;UAAE7D,GAAG,EAAEA,MAAM,IAAA;EAAK,OAAC,CAAC,CAAA;EAC/DmE,MAAAA,OAAO,GAAGN,OAAO,CAACc,IAAI,CACnBC,IAAS,IACRzM,MAAM,CAACmM,cAAc,CAACT,OAAO,EAAE,OAAO,EAAE;UAAE7D,GAAG,EAAEA,MAAM4E,IAAAA;SAAM,CAAC,EAC7DjM,KAAU,IACTR,MAAM,CAACmM,cAAc,CAACT,OAAO,EAAE,QAAQ,EAAE;UAAE7D,GAAG,EAAEA,MAAMrH,KAAAA;EAAM,OAAC,CACjE,CAAC,CAAA;EACH,KAAA;MAEA,IACEI,MAAM,KAAKiL,iBAAiB,CAACrL,KAAK,IAClCwL,OAAO,CAACvF,MAAM,YAAYiG,2BAAoB,EAC9C;EACA;EACA,MAAA,MAAMZ,mBAAmB,CAAA;EAC3B,KAAA;MAEA,IAAIlL,MAAM,KAAKiL,iBAAiB,CAACrL,KAAK,IAAI,CAACkC,YAAY,EAAE;EACvD;QACA,MAAMsJ,OAAO,CAACvF,MAAM,CAAA;EACtB,KAAA;EAEA,IAAA,IAAI7F,MAAM,KAAKiL,iBAAiB,CAACrL,KAAK,EAAE;EACtC;EACA,MAAA,oBAAO5H,gBAAA,CAAAyE,aAAA,CAACrE,YAAY,CAACsE,QAAQ,EAAA;EAACC,QAAAA,KAAK,EAAEyO,OAAQ;EAAC3J,QAAAA,QAAQ,EAAEK,YAAAA;EAAa,OAAE,CAAC,CAAA;EAC1E,KAAA;EAEA,IAAA,IAAI9B,MAAM,KAAKiL,iBAAiB,CAACK,OAAO,EAAE;EACxC;EACA,MAAA,oBAAOtT,gBAAA,CAAAyE,aAAA,CAACrE,YAAY,CAACsE,QAAQ,EAAA;EAACC,QAAAA,KAAK,EAAEyO,OAAQ;EAAC3J,QAAAA,QAAQ,EAAEA,QAAAA;EAAS,OAAE,CAAC,CAAA;EACtE,KAAA;;EAEA;EACA,IAAA,MAAM2J,OAAO,CAAA;EACf,GAAA;EACF,CAAA;;EAEA;EACA;EACA;EACA;EACA,SAASJ,YAAYA,CAAAe,KAAA,EAIlB;IAAA,IAJmB;EACpBtK,IAAAA,QAAAA;EAGF,GAAC,GAAAsK,KAAA,CAAA;EACC,EAAA,IAAIF,IAAI,GAAGnG,aAAa,EAAE,CAAA;EAC1B,EAAA,IAAIsG,QAAQ,GAAG,OAAOvK,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACoK,IAAI,CAAC,GAAGpK,QAAQ,CAAA;IACzE,oBAAOzJ,gBAAA,CAAAyE,aAAA,CAAAzE,gBAAA,CAAA2I,QAAA,EAAGqL,IAAAA,EAAAA,QAAW,CAAC,CAAA;EACxB,CAAA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASrB,wBAAwBA,CACtClJ,QAAyB,EACzB5D,UAAoB,EACL;EAAA,EAAA,IADfA,UAAoB,KAAA,KAAA,CAAA,EAAA;EAApBA,IAAAA,UAAoB,GAAG,EAAE,CAAA;EAAA,GAAA;IAEzB,IAAIX,MAAqB,GAAG,EAAE,CAAA;IAE9BlF,gBAAK,CAACiU,QAAQ,CAACC,OAAO,CAACzK,QAAQ,EAAE,CAAC7C,OAAO,EAAE0E,KAAK,KAAK;EACnD,IAAA,IAAI,eAACtL,gBAAK,CAACmU,cAAc,CAACvN,OAAO,CAAC,EAAE;EAClC;EACA;EACA,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAIwN,QAAQ,GAAG,CAAC,GAAGvO,UAAU,EAAEyF,KAAK,CAAC,CAAA;EAErC,IAAA,IAAI1E,OAAO,CAACyN,IAAI,KAAKrU,gBAAK,CAAC2I,QAAQ,EAAE;EACnC;EACAzD,MAAAA,MAAM,CAACf,IAAI,CAACmQ,KAAK,CACfpP,MAAM,EACNyN,wBAAwB,CAAC/L,OAAO,CAACqC,KAAK,CAACQ,QAAQ,EAAE2K,QAAQ,CAC3D,CAAC,CAAA;EACD,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,EACExN,OAAO,CAACyN,IAAI,KAAKrC,KAAK,CAAA,GADxB/Q,uBAAS,CAGL,KAAA,EAAA,GAAA,IAAA,OAAO2F,OAAO,CAACyN,IAAI,KAAK,QAAQ,GAAGzN,OAAO,CAACyN,IAAI,GAAGzN,OAAO,CAACyN,IAAI,CAACE,IAAI,8GAH9D,GAAA,KAAA,CAAA,CAAA;MAOT,EACE,CAAC3N,OAAO,CAACqC,KAAK,CAACqC,KAAK,IAAI,CAAC1E,OAAO,CAACqC,KAAK,CAACQ,QAAQ,CAAA7F,GADjD3C,uBAAS,CAAA,KAAA,EAEP,0CAA0C,CAAA,CAFnC,GAAA,KAAA,CAAA,CAAA;EAKT,IAAA,IAAI2E,KAAkB,GAAG;EACvBqE,MAAAA,EAAE,EAAErD,OAAO,CAACqC,KAAK,CAACgB,EAAE,IAAImK,QAAQ,CAAC1N,IAAI,CAAC,GAAG,CAAC;EAC1C8N,MAAAA,aAAa,EAAE5N,OAAO,CAACqC,KAAK,CAACuL,aAAa;EAC1C5N,MAAAA,OAAO,EAAEA,OAAO,CAACqC,KAAK,CAACrC,OAAO;EAC9BE,MAAAA,SAAS,EAAEF,OAAO,CAACqC,KAAK,CAACnC,SAAS;EAClCwE,MAAAA,KAAK,EAAE1E,OAAO,CAACqC,KAAK,CAACqC,KAAK;EAC1BvH,MAAAA,IAAI,EAAE6C,OAAO,CAACqC,KAAK,CAAClF,IAAI;EACxBqH,MAAAA,MAAM,EAAExE,OAAO,CAACqC,KAAK,CAACmC,MAAM;EAC5BsG,MAAAA,MAAM,EAAE9K,OAAO,CAACqC,KAAK,CAACyI,MAAM;EAC5B5H,MAAAA,YAAY,EAAElD,OAAO,CAACqC,KAAK,CAACa,YAAY;EACxCC,MAAAA,aAAa,EAAEnD,OAAO,CAACqC,KAAK,CAACc,aAAa;EAC1C0K,MAAAA,gBAAgB,EACd7N,OAAO,CAACqC,KAAK,CAACc,aAAa,IAAI,IAAI,IACnCnD,OAAO,CAACqC,KAAK,CAACa,YAAY,IAAI,IAAI;EACpC4K,MAAAA,gBAAgB,EAAE9N,OAAO,CAACqC,KAAK,CAACyL,gBAAgB;EAChDC,MAAAA,MAAM,EAAE/N,OAAO,CAACqC,KAAK,CAAC0L,MAAM;EAC5B5N,MAAAA,IAAI,EAAEH,OAAO,CAACqC,KAAK,CAAClC,IAAAA;OACrB,CAAA;EAED,IAAA,IAAIH,OAAO,CAACqC,KAAK,CAACQ,QAAQ,EAAE;EAC1B7D,MAAAA,KAAK,CAAC6D,QAAQ,GAAGkJ,wBAAwB,CACvC/L,OAAO,CAACqC,KAAK,CAACQ,QAAQ,EACtB2K,QACF,CAAC,CAAA;EACH,KAAA;EAEAlP,IAAAA,MAAM,CAACf,IAAI,CAACyB,KAAK,CAAC,CAAA;EACpB,GAAC,CAAC,CAAA;EAEF,EAAA,OAAOV,MAAM,CAAA;EACf,CAAA;;EAEA;EACA;EACA;EACO,SAAS0P,aAAaA,CAC3BnU,OAA4B,EACD;IAC3B,OAAOwG,cAAc,CAACxG,OAAO,CAAC,CAAA;EAChC;;ECtfA,SAASoU,kBAAkBA,CAACjP,KAAkB,EAAE;EAC9C,EAAA,IAAIkP,OAA6D,GAAG;EAClE;EACA;MACAL,gBAAgB,EAAE7O,KAAK,CAACmE,aAAa,IAAI,IAAI,IAAInE,KAAK,CAACkE,YAAY,IAAI,IAAA;KACxE,CAAA;IAED,IAAIlE,KAAK,CAACkB,SAAS,EAAE;EACnB,IAAa;QACX,IAAIlB,KAAK,CAACgB,OAAO,EAAE;EACjBhD,QAAAC,qBAAO,CACL,KAAK,EACL,wEAAwE,GACtE,2BACJ,CAAC,CAAA,CAAA;EACH,OAAA;EACF,KAAA;EACAuD,IAAAA,MAAM,CAACC,MAAM,CAACyN,OAAO,EAAE;QACrBlO,OAAO,eAAE5G,gBAAK,CAACyE,aAAa,CAACmB,KAAK,CAACkB,SAAS,CAAC;EAC7CA,MAAAA,SAAS,EAAED,SAAAA;EACb,KAAC,CAAC,CAAA;EACJ,GAAA;IAEA,IAAIjB,KAAK,CAACoF,eAAe,EAAE;EACzB,IAAa;QACX,IAAIpF,KAAK,CAACqF,sBAAsB,EAAE;EAChCrH,QAAAC,qBAAO,CACL,KAAK,EACL,6FAA6F,GAC3F,iCACJ,CAAC,CAAA,CAAA;EACH,OAAA;EACF,KAAA;EACAuD,IAAAA,MAAM,CAACC,MAAM,CAACyN,OAAO,EAAE;QACrB7J,sBAAsB,eAAEjL,gBAAK,CAACyE,aAAa,CAACmB,KAAK,CAACoF,eAAe,CAAC;EAClEA,MAAAA,eAAe,EAAEnE,SAAAA;EACnB,KAAC,CAAC,CAAA;EACJ,GAAA;IAEA,IAAIjB,KAAK,CAACmE,aAAa,EAAE;EACvB,IAAa;QACX,IAAInE,KAAK,CAACkE,YAAY,EAAE;EACtBlG,QAAAC,qBAAO,CACL,KAAK,EACL,iFAAiF,GAC/E,+BACJ,CAAC,CAAA,CAAA;EACH,OAAA;EACF,KAAA;EACAuD,IAAAA,MAAM,CAACC,MAAM,CAACyN,OAAO,EAAE;QACrBhL,YAAY,eAAE9J,gBAAK,CAACyE,aAAa,CAACmB,KAAK,CAACmE,aAAa,CAAC;EACtDA,MAAAA,aAAa,EAAElD,SAAAA;EACjB,KAAC,CAAC,CAAA;EACJ,GAAA;EAEA,EAAA,OAAOiO,OAAO,CAAA;EAChB,CAAA;EAEO,SAASC,kBAAkBA,CAChC7P,MAAqB,EACrB2L,IAQC,EACY;EACb,EAAA,OAAOmE,mBAAY,CAAC;EAClB9T,IAAAA,QAAQ,EAAE2P,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAE3P,QAAQ;EACxB6B,IAAAA,MAAM,EAAAwE,QAAA,CAAA,EAAA,EACDsJ,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAE9N,MAAM,EAAA;EACfkS,MAAAA,kBAAkB,EAAE,IAAA;OACrB,CAAA;MACDxD,OAAO,EAAEF,0BAAmB,CAAC;EAC3BH,MAAAA,cAAc,EAAEP,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEO,cAAc;EACpCC,MAAAA,YAAY,EAAER,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEQ,YAAAA;EACtB,KAAC,CAAC;EACF6D,IAAAA,aAAa,EAAErE,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEqE,aAAa;MAClChQ,MAAM;MACN2P,kBAAkB;EAClBM,IAAAA,YAAY,EAAEtE,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEsE,YAAY;EAChCC,IAAAA,uBAAuB,EAAEvE,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEuE,uBAAAA;EACjC,GAAC,CAAC,CAACC,UAAU,EAAE,CAAA;EACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}