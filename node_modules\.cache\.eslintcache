[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\components\\Dashboard.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\components\\StatsCard.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\components\\PropertyChart.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\components\\AgentsList.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\components\\AllAgents.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\components\\EditAgentModal.js": "8"}, {"size": 232, "mtime": 1752924823046, "results": "9", "hashOfConfig": "10"}, {"size": 829, "mtime": 1752925395413, "results": "11", "hashOfConfig": "10"}, {"size": 4638, "mtime": 1752926425284, "results": "12", "hashOfConfig": "10"}, {"size": 1023, "mtime": 1752924863504, "results": "13", "hashOfConfig": "10"}, {"size": 2860, "mtime": 1752926406508, "results": "14", "hashOfConfig": "10"}, {"size": 3668, "mtime": 1752925462828, "results": "15", "hashOfConfig": "10"}, {"size": 13967, "mtime": 1752925754898, "results": "16", "hashOfConfig": "10"}, {"size": 6359, "mtime": 1752925643041, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "enrtfh", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\components\\StatsCard.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\components\\PropertyChart.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\components\\AgentsList.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\components\\AllAgents.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Dash\\src\\components\\EditAgentModal.js", [], []]