import React from 'react';
import { Paper, Typography, Box } from '@mui/material';
import { Bar<PERSON>hart } from '@mui/x-charts/BarChart';

const PropertyChart = ({ title, data, height = 300, isComparison = false }) => {
  // Prepare data for the chart
  const prepareChartData = () => {
    if (isComparison) {
      // For comparison charts (like Property Quick Filters)
      return {
        xAxis: [{
          scaleType: 'band',
          data: data.map(item => item.month),
          categoryGapRatio: 0.3,
          barGapRatio: 0.1,
        }],
        series: [
          {
            data: data.map(item => item.rent),
            label: 'Rent',
            color: '#e0e0e0',
          },
          {
            data: data.map(item => item.sell),
            label: 'Sell',
            color: '#2196f3',
          },
        ],
      };
    } else {
      // For single series charts (like Property Type) - show individual bars, not aggregated
      // Create simplified labels for x-axis to match screenshot
      const xAxisLabels = data.map((item, index) => {
        const shortLabels = {
          'Condo': 'Condo',
          'Single Family': 'Single Family',
          'Apartment': 'Apartment',
          'Land': 'Land'
        };
        return shortLabels[item.type] || item.type;
      });

      return {
        xAxis: [{
          scaleType: 'band',
          data: xAxisLabels,
          categoryGapRatio: 0.1,
        }],
        series: [
          {
            data: data.map(item => item.value),
            // Highlight the apartment bar that has value 45
            color: data.map((item, index) =>
              (item.type === 'Apartment' && item.value === 45) ? '#2196f3' : '#e0e0e0'
            ),
          },
        ],
      };
    }
  };

  const chartConfig = prepareChartData();

  return (
    <Paper 
      sx={{ 
        p: 3, 
        height: height + 80,
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        borderRadius: 2,
      }}
    >
      <Typography 
        variant="h6" 
        component="h2" 
        sx={{ 
          mb: 2,
          fontWeight: 600,
          color: 'text.primary'
        }}
      >
        {title}
      </Typography>
      <Box sx={{ width: '100%', height: height }}>
        <BarChart
          xAxis={chartConfig.xAxis}
          series={chartConfig.series}
          width={undefined}
          height={height}
          margin={{
            left: 50,
            right: 50,
            top: 20,
            bottom: 50,
          }}
          colors={isComparison ? ['#e0e0e0', '#2196f3'] : undefined}
          slotProps={{
            legend: isComparison ? {
              direction: 'row',
              position: { vertical: 'top', horizontal: 'right' },
            } : undefined,
          }}
        />
      </Box>
    </Paper>
  );
};

export default PropertyChart;
