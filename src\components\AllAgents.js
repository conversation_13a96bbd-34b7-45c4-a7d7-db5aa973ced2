import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Breadcrumbs,
  Link,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  TablePagination,
  Avatar,
  Checkbox,
} from '@mui/material';
import {
  Search as SearchIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import EditAgentModal from './EditAgentModal';

const AllAgents = () => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [selected, setSelected] = useState([]);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);

  // Sample agent data
  const agentsData = [
    {
      id: 1,
      name: '<PERSON>',
      phone: '(*************',
      email: '<EMAIL>',
      joinDate: 'Jan 12, 2025',
      commission: 'Yes',
      status: 'Verified',
      runningLead: 'Verified',
      floridaLicense: 'FL123456789',
    },
    {
      id: 2,
      name: 'Shafin Desai',
      phone: '(*************',
      email: '<EMAIL>',
      joinDate: 'Jan 10, 2025',
      commission: 'Yes',
      status: 'Verified',
      runningLead: 'Verified',
      floridaLicense: 'FL987654321',
    },
    {
      id: 3,
      name: 'Michael Johnson',
      phone: '(*************',
      email: '<EMAIL>',
      joinDate: 'Jan 08, 2025',
      commission: 'No',
      status: 'Pending',
      runningLead: 'Pending',
      floridaLicense: 'FL456789123',
    },
    {
      id: 4,
      name: 'Jessica Morgan',
      phone: '(*************',
      email: '<EMAIL>',
      joinDate: 'Jan 12, 2025',
      commission: 'Yes',
      status: 'Verified',
      runningLead: 'Verified',
    },
    {
      id: 5,
      name: 'Jessica Morgan',
      phone: '(*************',
      email: '<EMAIL>',
      joinDate: 'Jan 12, 2025',
      commission: 'Yes',
      status: 'Verified',
      runningLead: 'Verified',
    },
    {
      id: 6,
      name: 'Jessica Morgan',
      phone: '(*************',
      email: '<EMAIL>',
      joinDate: 'Jan 12, 2025',
      commission: 'Yes',
      status: 'Verified',
      runningLead: 'Verified',
    },
    {
      id: 7,
      name: 'Jessica Morgan',
      phone: '(*************',
      email: '<EMAIL>',
      joinDate: 'Jan 12, 2025',
      commission: 'Yes',
      status: 'Verified',
      runningLead: 'Verified',
    },
    {
      id: 8,
      name: 'Jessica Morgan',
      phone: '(*************',
      email: '<EMAIL>',
      joinDate: 'Jan 12, 2025',
      commission: 'Yes',
      status: 'Verified',
      runningLead: 'Verified',
    },
    {
      id: 9,
      name: 'Jessica Morgan',
      phone: '(*************',
      email: '<EMAIL>',
      joinDate: 'Jan 12, 2025',
      commission: 'No',
      status: 'Verified',
      runningLead: 'Verified',
    },
    {
      id: 10,
      name: 'Jessica Morgan',
      phone: '(*************',
      email: '<EMAIL>',
      joinDate: 'Jan 12, 2025',
      commission: 'No',
      status: 'Verified',
      runningLead: 'Verified',
    },
  ];

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = agentsData.map((n) => n.id);
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };

  const handleClick = (event, id) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1),
      );
    }
    setSelected(newSelected);
  };

  const isSelected = (id) => selected.indexOf(id) !== -1;

  const handleEditAgent = (agent) => {
    setSelectedAgent(agent);
    setEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setEditModalOpen(false);
    setSelectedAgent(null);
  };

  const handleSaveAgent = (updatedAgentData) => {
    // Here you would typically update the agent data in your backend/state
    console.log('Saving agent data:', updatedAgentData);
    // For now, just log the data - you can implement the actual save logic later
  };

  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getAvatarColor = (name) => {
    const colors = [
      '#f44336', '#e91e63', '#9c27b0', '#673ab7',
      '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4',
      '#009688', '#4caf50', '#8bc34a', '#cddc39',
    ];
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'verified':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  const filteredAgents = agentsData.filter(agent =>
    agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.phone.includes(searchTerm)
  );

  const paginatedAgents = filteredAgents.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <Box sx={{ backgroundColor: '#f5f5f5', minHeight: '100vh', py: 3 }}>
      <Container maxWidth="xl">
        {/* Breadcrumbs */}
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 3 }}>
          <Link underline="hover" color="inherit" href="/">
            Dashboard
          </Link>
          <Typography color="primary">All Agents</Typography>
        </Breadcrumbs>

        {/* Search Bar */}
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            placeholder="Search by username, email"
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
            }}
            sx={{
              maxWidth: 400,
              backgroundColor: 'white',
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
              },
            }}
          />
        </Box>

        {/* Table */}
        <Paper sx={{ width: '100%', mb: 2, borderRadius: 2 }}>
          <TableContainer>
            <Table sx={{ minWidth: 750 }} aria-labelledby="tableTitle">
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                  <TableCell padding="checkbox">
                    <Checkbox
                      color="primary"
                      indeterminate={selected.length > 0 && selected.length < agentsData.length}
                      checked={agentsData.length > 0 && selected.length === agentsData.length}
                      onChange={handleSelectAllClick}
                    />
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Name</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Phone Number</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Email</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Commission</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Running Lead</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Joining Date</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedAgents.map((agent, index) => {
                  const isItemSelected = isSelected(agent.id);
                  const labelId = `enhanced-table-checkbox-${index}`;

                  return (
                    <TableRow
                      hover
                      onClick={(event) => handleClick(event, agent.id)}
                      role="checkbox"
                      aria-checked={isItemSelected}
                      tabIndex={-1}
                      key={agent.id}
                      selected={isItemSelected}
                      sx={{ cursor: 'pointer' }}
                    >
                      <TableCell padding="checkbox">
                        <Checkbox
                          color="primary"
                          checked={isItemSelected}
                          inputProps={{
                            'aria-labelledby': labelId,
                          }}
                        />
                      </TableCell>
                      <TableCell component="th" id={labelId} scope="row">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar
                            sx={{
                              bgcolor: getAvatarColor(agent.name),
                              width: 32,
                              height: 32,
                              fontSize: '0.875rem',
                            }}
                          >
                            {getInitials(agent.name)}
                          </Avatar>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {agent.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="primary" sx={{ fontWeight: 500 }}>
                          {agent.phone}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {agent.email}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={agent.commission}
                          color={agent.commission === 'Yes' ? 'success' : 'error'}
                          size="small"
                          sx={{ minWidth: 60 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={agent.runningLead}
                          color={getStatusColor(agent.runningLead)}
                          size="small"
                          sx={{ minWidth: 80 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {agent.joinDate}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Chip
                            label={agent.status}
                            color={getStatusColor(agent.status)}
                            size="small"
                            sx={{ minWidth: 80 }}
                          />
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleEditAgent(agent)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
          
          {/* Pagination */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Showing {page * rowsPerPage + 1} to {Math.min((page + 1) * rowsPerPage, filteredAgents.length)} of {filteredAgents.length}
            </Typography>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={filteredAgents.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              sx={{
                '& .MuiTablePagination-toolbar': {
                  minHeight: 'auto',
                },
                '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
                  margin: 0,
                },
              }}
            />
          </Box>
        </Paper>
      </Container>

      {/* Edit Agent Modal */}
      <EditAgentModal
        open={editModalOpen}
        onClose={handleCloseEditModal}
        agent={selectedAgent}
        onSave={handleSaveAgent}
      />
    </Box>
  );
};

export default AllAgents;
