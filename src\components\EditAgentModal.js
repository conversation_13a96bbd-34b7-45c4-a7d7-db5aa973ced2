import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  IconButton,
  Box,
  Typography,
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';

const EditAgentModal = ({ open, onClose, agent, onSave }) => {
  const [formData, setFormData] = useState({
    email: agent?.email || '',
    name: agent?.name || '',
    floridaLicense: agent?.floridaLicense || '',
  });

  const handleChange = (field) => (event) => {
    setFormData({
      ...formData,
      [field]: event.target.value,
    });
  };

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  const handleClose = () => {
    // Reset form data when closing
    setFormData({
      email: agent?.email || '',
      name: agent?.name || '',
      floridaLicense: agent?.floridaLicense || '',
    });
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
        },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          pb: 2,
          px: 3,
          pt: 3,
        }}
      >
        <Typography
          variant="h6"
          component="h2"
          sx={{
            fontWeight: 600,
            color: 'text.primary',
            fontSize: '1.25rem',
          }}
        >
          Edit Agent Details
        </Typography>
        <IconButton
          onClick={handleClose}
          sx={{
            color: 'text.secondary',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ px: 3, pb: 2 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 1 }}>
          {/* Email Field */}
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 500,
                color: 'text.primary',
                mb: 1,
                fontSize: '0.875rem',
              }}
            >
              Email
            </Typography>
            <TextField
              fullWidth
              value={formData.email}
              onChange={handleChange('email')}
              placeholder="<EMAIL>"
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: '#f8f9fa',
                  '& fieldset': {
                    borderColor: '#e0e0e0',
                  },
                  '&:hover fieldset': {
                    borderColor: '#bdbdbd',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: 'primary.main',
                  },
                },
                '& .MuiInputBase-input': {
                  fontSize: '0.875rem',
                  py: 1.5,
                },
              }}
            />
          </Box>

          {/* Name Field */}
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 500,
                color: 'text.primary',
                mb: 1,
                fontSize: '0.875rem',
              }}
            >
              Name
            </Typography>
            <TextField
              fullWidth
              value={formData.name}
              onChange={handleChange('name')}
              placeholder="Shafin Desai"
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: '#f8f9fa',
                  '& fieldset': {
                    borderColor: '#e0e0e0',
                  },
                  '&:hover fieldset': {
                    borderColor: '#bdbdbd',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: 'primary.main',
                  },
                },
                '& .MuiInputBase-input': {
                  fontSize: '0.875rem',
                  py: 1.5,
                },
              }}
            />
          </Box>

          {/* Florida License Field */}
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 500,
                color: 'text.primary',
                mb: 1,
                fontSize: '0.875rem',
              }}
            >
              Florida License
            </Typography>
            <TextField
              fullWidth
              value={formData.floridaLicense}
              onChange={handleChange('floridaLicense')}
              placeholder="License Number"
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: '#f8f9fa',
                  '& fieldset': {
                    borderColor: '#e0e0e0',
                  },
                  '&:hover fieldset': {
                    borderColor: '#bdbdbd',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: 'primary.main',
                  },
                },
                '& .MuiInputBase-input': {
                  fontSize: '0.875rem',
                  py: 1.5,
                },
              }}
            />
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3, pt: 2 }}>
        <Button
          onClick={handleSave}
          variant="contained"
          fullWidth
          sx={{
            backgroundColor: '#1565c0',
            color: 'white',
            fontWeight: 600,
            fontSize: '0.875rem',
            textTransform: 'none',
            borderRadius: 2,
            py: 1.5,
            '&:hover': {
              backgroundColor: '#0d47a1',
            },
            boxShadow: '0 2px 8px rgba(21, 101, 192, 0.3)',
          }}
        >
          Update
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditAgentModal;
