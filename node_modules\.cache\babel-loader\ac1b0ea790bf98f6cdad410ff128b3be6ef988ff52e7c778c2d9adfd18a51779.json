{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Dash\\\\src\\\\components\\\\AgentsList.js\";\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Paper, Typography, List, ListItem, ListItemAvatar, ListItemText, Avatar, Box, Button } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AgentsList = ({\n  agents\n}) => {\n  // Generate a color for avatar based on name\n  const getAvatarColor = name => {\n    const colors = ['#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4caf50', '#8bc34a', '#cddc39', '#ffeb3b', '#ffc107', '#ff9800', '#ff5722'];\n    const index = name.charCodeAt(0) % colors.length;\n    return colors[index];\n  };\n\n  // Get initials from name\n  const getInitials = name => {\n    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      height: '100%',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n      borderRadius: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3,\n        pb: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        component: \"h2\",\n        sx: {\n          fontWeight: 600,\n          color: 'text.primary',\n          mb: 2\n        },\n        children: \"Recent Registered Agent\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(List, {\n      sx: {\n        pt: 0,\n        maxHeight: 600,\n        overflow: 'auto'\n      },\n      children: agents.map((agent, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n        sx: {\n          px: 3,\n          py: 1.5,\n          '&:hover': {\n            backgroundColor: 'rgba(0, 0, 0, 0.04)'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n          children: /*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              bgcolor: getAvatarColor(agent.name),\n              width: 40,\n              height: 40,\n              fontSize: '0.875rem',\n              fontWeight: 600\n            },\n            children: getInitials(agent.name)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary',\n              fontSize: '0.875rem'\n            },\n            children: agent.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 17\n          }, this),\n          secondary: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: 'text.secondary',\n                fontSize: '0.75rem',\n                mb: 0.25\n              },\n              children: agent.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: 'text.secondary',\n                fontSize: '0.75rem'\n              },\n              children: agent.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_c = AgentsList;\nexport default AgentsList;\nvar _c;\n$RefreshReg$(_c, \"AgentsList\");", "map": {"version": 3, "names": ["React", "useNavigate", "Paper", "Typography", "List", "ListItem", "ListItemAvatar", "ListItemText", "Avatar", "Box", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "AgentsList", "agents", "getAvatarColor", "name", "colors", "index", "charCodeAt", "length", "getInitials", "split", "map", "word", "char<PERSON>t", "join", "toUpperCase", "slice", "sx", "height", "boxShadow", "borderRadius", "children", "p", "pb", "variant", "component", "fontWeight", "color", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pt", "maxHeight", "overflow", "agent", "px", "py", "backgroundColor", "bgcolor", "width", "fontSize", "primary", "secondary", "email", "role", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Dash/src/components/AgentsList.js"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Paper,\n  Typography,\n  List,\n  ListItem,\n  ListItemAvatar,\n  ListItemText,\n  Avatar,\n  Box,\n  Button,\n} from '@mui/material';\n\nconst AgentsList = ({ agents }) => {\n  // Generate a color for avatar based on name\n  const getAvatarColor = (name) => {\n    const colors = [\n      '#f44336', '#e91e63', '#9c27b0', '#673ab7',\n      '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4',\n      '#009688', '#4caf50', '#8bc34a', '#cddc39',\n      '#ffeb3b', '#ffc107', '#ff9800', '#ff5722'\n    ];\n    const index = name.charCodeAt(0) % colors.length;\n    return colors[index];\n  };\n\n  // Get initials from name\n  const getInitials = (name) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <Paper \n      sx={{ \n        height: '100%',\n        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n        borderRadius: 2,\n      }}\n    >\n      <Box sx={{ p: 3, pb: 1 }}>\n        <Typography \n          variant=\"h6\" \n          component=\"h2\" \n          sx={{ \n            fontWeight: 600,\n            color: 'text.primary',\n            mb: 2\n          }}\n        >\n          Recent Registered Agent\n        </Typography>\n      </Box>\n      \n      <List sx={{ pt: 0, maxHeight: 600, overflow: 'auto' }}>\n        {agents.map((agent, index) => (\n          <ListItem \n            key={index}\n            sx={{ \n              px: 3,\n              py: 1.5,\n              '&:hover': {\n                backgroundColor: 'rgba(0, 0, 0, 0.04)',\n              }\n            }}\n          >\n            <ListItemAvatar>\n              <Avatar \n                sx={{ \n                  bgcolor: getAvatarColor(agent.name),\n                  width: 40,\n                  height: 40,\n                  fontSize: '0.875rem',\n                  fontWeight: 600\n                }}\n              >\n                {getInitials(agent.name)}\n              </Avatar>\n            </ListItemAvatar>\n            <ListItemText\n              primary={\n                <Typography \n                  variant=\"body1\" \n                  sx={{ \n                    fontWeight: 500,\n                    color: 'text.primary',\n                    fontSize: '0.875rem'\n                  }}\n                >\n                  {agent.name}\n                </Typography>\n              }\n              secondary={\n                <Box>\n                  <Typography \n                    variant=\"body2\" \n                    sx={{ \n                      color: 'text.secondary',\n                      fontSize: '0.75rem',\n                      mb: 0.25\n                    }}\n                  >\n                    {agent.email}\n                  </Typography>\n                  <Typography \n                    variant=\"body2\" \n                    sx={{ \n                      color: 'text.secondary',\n                      fontSize: '0.75rem'\n                    }}\n                  >\n                    {agent.role}\n                  </Typography>\n                </Box>\n              }\n            />\n          </ListItem>\n        ))}\n      </List>\n    </Paper>\n  );\n};\n\nexport default AgentsList;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,MAAM,EACNC,GAAG,EACHC,MAAM,QACD,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EACjC;EACA,MAAMC,cAAc,GAAIC,IAAI,IAAK;IAC/B,MAAMC,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC3C;IACD,MAAMC,KAAK,GAAGF,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,GAAGF,MAAM,CAACG,MAAM;IAChD,OAAOH,MAAM,CAACC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAMG,WAAW,GAAIL,IAAI,IAAK;IAC5B,OAAOA,IAAI,CACRM,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC3BC,IAAI,CAAC,EAAE,CAAC,CACRC,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EAED,oBACEhB,OAAA,CAACV,KAAK;IACJ2B,EAAE,EAAE;MACFC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,2BAA2B;MACtCC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBAEFrB,OAAA,CAACH,GAAG;MAACoB,EAAE,EAAE;QAAEK,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eACvBrB,OAAA,CAACT,UAAU;QACTiC,OAAO,EAAC,IAAI;QACZC,SAAS,EAAC,IAAI;QACdR,EAAE,EAAE;UACFS,UAAU,EAAE,GAAG;UACfC,KAAK,EAAE,cAAc;UACrBC,EAAE,EAAE;QACN,CAAE;QAAAP,QAAA,EACH;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAENhC,OAAA,CAACR,IAAI;MAACyB,EAAE,EAAE;QAAEgB,EAAE,EAAE,CAAC;QAAEC,SAAS,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAd,QAAA,EACnDnB,MAAM,CAACS,GAAG,CAAC,CAACyB,KAAK,EAAE9B,KAAK,kBACvBN,OAAA,CAACP,QAAQ;QAEPwB,EAAE,EAAE;UACFoB,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,GAAG;UACP,SAAS,EAAE;YACTC,eAAe,EAAE;UACnB;QACF,CAAE;QAAAlB,QAAA,gBAEFrB,OAAA,CAACN,cAAc;UAAA2B,QAAA,eACbrB,OAAA,CAACJ,MAAM;YACLqB,EAAE,EAAE;cACFuB,OAAO,EAAErC,cAAc,CAACiC,KAAK,CAAChC,IAAI,CAAC;cACnCqC,KAAK,EAAE,EAAE;cACTvB,MAAM,EAAE,EAAE;cACVwB,QAAQ,EAAE,UAAU;cACpBhB,UAAU,EAAE;YACd,CAAE;YAAAL,QAAA,EAEDZ,WAAW,CAAC2B,KAAK,CAAChC,IAAI;UAAC;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACjBhC,OAAA,CAACL,YAAY;UACXgD,OAAO,eACL3C,OAAA,CAACT,UAAU;YACTiC,OAAO,EAAC,OAAO;YACfP,EAAE,EAAE;cACFS,UAAU,EAAE,GAAG;cACfC,KAAK,EAAE,cAAc;cACrBe,QAAQ,EAAE;YACZ,CAAE;YAAArB,QAAA,EAEDe,KAAK,CAAChC;UAAI;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACb;UACDY,SAAS,eACP5C,OAAA,CAACH,GAAG;YAAAwB,QAAA,gBACFrB,OAAA,CAACT,UAAU;cACTiC,OAAO,EAAC,OAAO;cACfP,EAAE,EAAE;gBACFU,KAAK,EAAE,gBAAgB;gBACvBe,QAAQ,EAAE,SAAS;gBACnBd,EAAE,EAAE;cACN,CAAE;cAAAP,QAAA,EAEDe,KAAK,CAACS;YAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACbhC,OAAA,CAACT,UAAU;cACTiC,OAAO,EAAC,OAAO;cACfP,EAAE,EAAE;gBACFU,KAAK,EAAE,gBAAgB;gBACvBe,QAAQ,EAAE;cACZ,CAAE;cAAArB,QAAA,EAEDe,KAAK,CAACU;YAAI;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA,GA1DG1B,KAAK;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2DF,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ,CAAC;AAACe,EAAA,GAhHI9C,UAAU;AAkHhB,eAAeA,UAAU;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}