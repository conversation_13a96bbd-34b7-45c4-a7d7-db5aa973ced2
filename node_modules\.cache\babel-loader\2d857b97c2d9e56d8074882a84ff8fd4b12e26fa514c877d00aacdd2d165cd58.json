{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Dash\\\\src\\\\components\\\\AllAgents.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Container, Typography, Breadcrumbs, Link, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, InputAdornment, Chip, IconButton, TablePagination, Avatar, Checkbox } from '@mui/material';\nimport { Search as SearchIcon, Edit as EditIcon } from '@mui/icons-material';\nimport EditAgentModal from './EditAgentModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllAgents = () => {\n  _s();\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selected, setSelected] = useState([]);\n\n  // Sample agent data\n  const agentsData = [{\n    id: 1,\n    name: '<PERSON>',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 2,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 3,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 4,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 5,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 6,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 7,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 8,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 9,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'No',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 10,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'No',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }];\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n  const handleChangeRowsPerPage = event => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n  const handleSearchChange = event => {\n    setSearchTerm(event.target.value);\n  };\n  const handleSelectAllClick = event => {\n    if (event.target.checked) {\n      const newSelected = agentsData.map(n => n.id);\n      setSelected(newSelected);\n      return;\n    }\n    setSelected([]);\n  };\n  const handleClick = (event, id) => {\n    const selectedIndex = selected.indexOf(id);\n    let newSelected = [];\n    if (selectedIndex === -1) {\n      newSelected = newSelected.concat(selected, id);\n    } else if (selectedIndex === 0) {\n      newSelected = newSelected.concat(selected.slice(1));\n    } else if (selectedIndex === selected.length - 1) {\n      newSelected = newSelected.concat(selected.slice(0, -1));\n    } else if (selectedIndex > 0) {\n      newSelected = newSelected.concat(selected.slice(0, selectedIndex), selected.slice(selectedIndex + 1));\n    }\n    setSelected(newSelected);\n  };\n  const isSelected = id => selected.indexOf(id) !== -1;\n  const getInitials = name => {\n    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);\n  };\n  const getAvatarColor = name => {\n    const colors = ['#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4caf50', '#8bc34a', '#cddc39'];\n    const index = name.charCodeAt(0) % colors.length;\n    return colors[index];\n  };\n  const getStatusColor = status => {\n    switch (status.toLowerCase()) {\n      case 'verified':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'rejected':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const filteredAgents = agentsData.filter(agent => agent.name.toLowerCase().includes(searchTerm.toLowerCase()) || agent.email.toLowerCase().includes(searchTerm.toLowerCase()) || agent.phone.includes(searchTerm));\n  const paginatedAgents = filteredAgents.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      backgroundColor: '#f5f5f5',\n      minHeight: '100vh',\n      py: 3\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: [/*#__PURE__*/_jsxDEV(Breadcrumbs, {\n        \"aria-label\": \"breadcrumb\",\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          underline: \"hover\",\n          color: \"inherit\",\n          href: \"/\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"primary\",\n          children: \"All Agents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Search by username, email\",\n          value: searchTerm,\n          onChange: handleSearchChange,\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                color: \"action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this)\n          },\n          sx: {\n            maxWidth: 400,\n            backgroundColor: 'white',\n            '& .MuiOutlinedInput-root': {\n              borderRadius: 2\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          width: '100%',\n          mb: 2,\n          borderRadius: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            sx: {\n              minWidth: 750\n            },\n            \"aria-labelledby\": \"tableTitle\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  backgroundColor: '#f8f9fa'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  padding: \"checkbox\",\n                  children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                    color: \"primary\",\n                    indeterminate: selected.length > 0 && selected.length < agentsData.length,\n                    checked: agentsData.length > 0 && selected.length === agentsData.length,\n                    onChange: handleSelectAllClick\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Phone Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Commission\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Running Lead\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Joining Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: paginatedAgents.map((agent, index) => {\n                const isItemSelected = isSelected(agent.id);\n                const labelId = `enhanced-table-checkbox-${index}`;\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  hover: true,\n                  onClick: event => handleClick(event, agent.id),\n                  role: \"checkbox\",\n                  \"aria-checked\": isItemSelected,\n                  tabIndex: -1,\n                  selected: isItemSelected,\n                  sx: {\n                    cursor: 'pointer'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    padding: \"checkbox\",\n                    children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                      color: \"primary\",\n                      checked: isItemSelected,\n                      inputProps: {\n                        'aria-labelledby': labelId\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    component: \"th\",\n                    id: labelId,\n                    scope: \"row\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                        sx: {\n                          bgcolor: getAvatarColor(agent.name),\n                          width: 32,\n                          height: 32,\n                          fontSize: '0.875rem'\n                        },\n                        children: getInitials(agent.name)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 310,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 500\n                        },\n                        children: agent.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 309,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"primary\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: agent.phone\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: agent.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: agent.commission,\n                      color: agent.commission === 'Yes' ? 'success' : 'error',\n                      size: \"small\",\n                      sx: {\n                        minWidth: 60\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: agent.runningLead,\n                      color: getStatusColor(agent.runningLead),\n                      size: \"small\",\n                      sx: {\n                        minWidth: 80\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: agent.joinDate\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Chip, {\n                        label: agent.status,\n                        color: getStatusColor(agent.status),\n                        size: \"small\",\n                        sx: {\n                          minWidth: 80\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 358,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"primary\",\n                        children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 365,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 364,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this)]\n                }, agent.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"Showing \", page * rowsPerPage + 1, \" to \", Math.min((page + 1) * rowsPerPage, filteredAgents.length), \" of \", filteredAgents.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n            rowsPerPageOptions: [5, 10, 25],\n            component: \"div\",\n            count: filteredAgents.length,\n            rowsPerPage: rowsPerPage,\n            page: page,\n            onPageChange: handleChangePage,\n            onRowsPerPageChange: handleChangeRowsPerPage,\n            sx: {\n              '& .MuiTablePagination-toolbar': {\n                minHeight: 'auto'\n              },\n              '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {\n                margin: 0\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 226,\n    columnNumber: 5\n  }, this);\n};\n_s(AllAgents, \"uRXP6hJHVDOexqGpv2b5QafCXk0=\");\n_c = AllAgents;\nexport default AllAgents;\nvar _c;\n$RefreshReg$(_c, \"AllAgents\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Container", "Typography", "Breadcrumbs", "Link", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TextField", "InputAdornment", "Chip", "IconButton", "TablePagination", "Avatar", "Checkbox", "Search", "SearchIcon", "Edit", "EditIcon", "EditAgentModal", "jsxDEV", "_jsxDEV", "AllAgents", "_s", "page", "setPage", "rowsPerPage", "setRowsPerPage", "searchTerm", "setSearchTerm", "selected", "setSelected", "agentsData", "id", "name", "phone", "email", "joinDate", "commission", "status", "runningLead", "handleChangePage", "event", "newPage", "handleChangeRowsPerPage", "parseInt", "target", "value", "handleSearchChange", "handleSelectAllClick", "checked", "newSelected", "map", "n", "handleClick", "selectedIndex", "indexOf", "concat", "slice", "length", "isSelected", "getInitials", "split", "word", "char<PERSON>t", "join", "toUpperCase", "getAvatarColor", "colors", "index", "charCodeAt", "getStatusColor", "toLowerCase", "filteredAgents", "filter", "agent", "includes", "paginatedAgents", "sx", "backgroundColor", "minHeight", "py", "children", "max<PERSON><PERSON><PERSON>", "mb", "underline", "color", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "placeholder", "onChange", "InputProps", "startAdornment", "position", "borderRadius", "width", "min<PERSON><PERSON><PERSON>", "padding", "indeterminate", "fontWeight", "isItemSelected", "labelId", "hover", "onClick", "role", "tabIndex", "cursor", "inputProps", "component", "scope", "display", "alignItems", "gap", "bgcolor", "height", "fontSize", "variant", "label", "size", "justifyContent", "p", "Math", "min", "rowsPerPageOptions", "count", "onPageChange", "onRowsPerPageChange", "margin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Dash/src/components/AllAgents.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Breadcrum<PERSON>,\n  Link,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TextField,\n  InputAdornment,\n  Chip,\n  IconButton,\n  TablePagination,\n  Avatar,\n  Checkbox,\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Edit as EditIcon,\n} from '@mui/icons-material';\nimport EditAgentModal from './EditAgentModal';\n\nconst AllAgents = () => {\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selected, setSelected] = useState([]);\n\n  // Sample agent data\n  const agentsData = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 2,\n      name: '<PERSON>',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 3,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 4,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 5,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 6,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 7,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 8,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 9,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'No',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 10,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'No',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n  ];\n\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n\n  const handleChangeRowsPerPage = (event) => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n\n  const handleSearchChange = (event) => {\n    setSearchTerm(event.target.value);\n  };\n\n  const handleSelectAllClick = (event) => {\n    if (event.target.checked) {\n      const newSelected = agentsData.map((n) => n.id);\n      setSelected(newSelected);\n      return;\n    }\n    setSelected([]);\n  };\n\n  const handleClick = (event, id) => {\n    const selectedIndex = selected.indexOf(id);\n    let newSelected = [];\n\n    if (selectedIndex === -1) {\n      newSelected = newSelected.concat(selected, id);\n    } else if (selectedIndex === 0) {\n      newSelected = newSelected.concat(selected.slice(1));\n    } else if (selectedIndex === selected.length - 1) {\n      newSelected = newSelected.concat(selected.slice(0, -1));\n    } else if (selectedIndex > 0) {\n      newSelected = newSelected.concat(\n        selected.slice(0, selectedIndex),\n        selected.slice(selectedIndex + 1),\n      );\n    }\n    setSelected(newSelected);\n  };\n\n  const isSelected = (id) => selected.indexOf(id) !== -1;\n\n  const getInitials = (name) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  const getAvatarColor = (name) => {\n    const colors = [\n      '#f44336', '#e91e63', '#9c27b0', '#673ab7',\n      '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4',\n      '#009688', '#4caf50', '#8bc34a', '#cddc39',\n    ];\n    const index = name.charCodeAt(0) % colors.length;\n    return colors[index];\n  };\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'verified':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'rejected':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const filteredAgents = agentsData.filter(agent =>\n    agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    agent.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    agent.phone.includes(searchTerm)\n  );\n\n  const paginatedAgents = filteredAgents.slice(\n    page * rowsPerPage,\n    page * rowsPerPage + rowsPerPage\n  );\n\n  return (\n    <Box sx={{ backgroundColor: '#f5f5f5', minHeight: '100vh', py: 3 }}>\n      <Container maxWidth=\"xl\">\n        {/* Breadcrumbs */}\n        <Breadcrumbs aria-label=\"breadcrumb\" sx={{ mb: 3 }}>\n          <Link underline=\"hover\" color=\"inherit\" href=\"/\">\n            Dashboard\n          </Link>\n          <Typography color=\"primary\">All Agents</Typography>\n        </Breadcrumbs>\n\n        {/* Search Bar */}\n        <Box sx={{ mb: 3 }}>\n          <TextField\n            fullWidth\n            placeholder=\"Search by username, email\"\n            value={searchTerm}\n            onChange={handleSearchChange}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon color=\"action\" />\n                </InputAdornment>\n              ),\n            }}\n            sx={{\n              maxWidth: 400,\n              backgroundColor: 'white',\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 2,\n              },\n            }}\n          />\n        </Box>\n\n        {/* Table */}\n        <Paper sx={{ width: '100%', mb: 2, borderRadius: 2 }}>\n          <TableContainer>\n            <Table sx={{ minWidth: 750 }} aria-labelledby=\"tableTitle\">\n              <TableHead>\n                <TableRow sx={{ backgroundColor: '#f8f9fa' }}>\n                  <TableCell padding=\"checkbox\">\n                    <Checkbox\n                      color=\"primary\"\n                      indeterminate={selected.length > 0 && selected.length < agentsData.length}\n                      checked={agentsData.length > 0 && selected.length === agentsData.length}\n                      onChange={handleSelectAllClick}\n                    />\n                  </TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Name</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Phone Number</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Email</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Commission</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Running Lead</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Joining Date</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Status</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {paginatedAgents.map((agent, index) => {\n                  const isItemSelected = isSelected(agent.id);\n                  const labelId = `enhanced-table-checkbox-${index}`;\n\n                  return (\n                    <TableRow\n                      hover\n                      onClick={(event) => handleClick(event, agent.id)}\n                      role=\"checkbox\"\n                      aria-checked={isItemSelected}\n                      tabIndex={-1}\n                      key={agent.id}\n                      selected={isItemSelected}\n                      sx={{ cursor: 'pointer' }}\n                    >\n                      <TableCell padding=\"checkbox\">\n                        <Checkbox\n                          color=\"primary\"\n                          checked={isItemSelected}\n                          inputProps={{\n                            'aria-labelledby': labelId,\n                          }}\n                        />\n                      </TableCell>\n                      <TableCell component=\"th\" id={labelId} scope=\"row\">\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                          <Avatar\n                            sx={{\n                              bgcolor: getAvatarColor(agent.name),\n                              width: 32,\n                              height: 32,\n                              fontSize: '0.875rem',\n                            }}\n                          >\n                            {getInitials(agent.name)}\n                          </Avatar>\n                          <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                            {agent.name}\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\" color=\"primary\" sx={{ fontWeight: 500 }}>\n                          {agent.phone}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {agent.email}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={agent.commission}\n                          color={agent.commission === 'Yes' ? 'success' : 'error'}\n                          size=\"small\"\n                          sx={{ minWidth: 60 }}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={agent.runningLead}\n                          color={getStatusColor(agent.runningLead)}\n                          size=\"small\"\n                          sx={{ minWidth: 80 }}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {agent.joinDate}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          <Chip\n                            label={agent.status}\n                            color={getStatusColor(agent.status)}\n                            size=\"small\"\n                            sx={{ minWidth: 80 }}\n                          />\n                          <IconButton size=\"small\" color=\"primary\">\n                            <EditIcon fontSize=\"small\" />\n                          </IconButton>\n                        </Box>\n                      </TableCell>\n                    </TableRow>\n                  );\n                })}\n              </TableBody>\n            </Table>\n          </TableContainer>\n          \n          {/* Pagination */}\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Showing {page * rowsPerPage + 1} to {Math.min((page + 1) * rowsPerPage, filteredAgents.length)} of {filteredAgents.length}\n            </Typography>\n            <TablePagination\n              rowsPerPageOptions={[5, 10, 25]}\n              component=\"div\"\n              count={filteredAgents.length}\n              rowsPerPage={rowsPerPage}\n              page={page}\n              onPageChange={handleChangePage}\n              onRowsPerPageChange={handleChangeRowsPerPage}\n              sx={{\n                '& .MuiTablePagination-toolbar': {\n                  minHeight: 'auto',\n                },\n                '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {\n                  margin: 0,\n                },\n              }}\n            />\n          </Box>\n        </Paper>\n      </Container>\n    </Box>\n  );\n};\n\nexport default AllAgents;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,eAAe,EACfC,MAAM,EACNC,QAAQ,QACH,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAMqC,UAAU,GAAG,CACjB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,EAAE;IACNC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC3ClB,OAAO,CAACkB,OAAO,CAAC;EAClB,CAAC;EAED,MAAMC,uBAAuB,GAAIF,KAAK,IAAK;IACzCf,cAAc,CAACkB,QAAQ,CAACH,KAAK,CAACI,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,CAAC;IAChDtB,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC;EAED,MAAMuB,kBAAkB,GAAIN,KAAK,IAAK;IACpCb,aAAa,CAACa,KAAK,CAACI,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;EAED,MAAME,oBAAoB,GAAIP,KAAK,IAAK;IACtC,IAAIA,KAAK,CAACI,MAAM,CAACI,OAAO,EAAE;MACxB,MAAMC,WAAW,GAAGnB,UAAU,CAACoB,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACpB,EAAE,CAAC;MAC/CF,WAAW,CAACoB,WAAW,CAAC;MACxB;IACF;IACApB,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EAED,MAAMuB,WAAW,GAAGA,CAACZ,KAAK,EAAET,EAAE,KAAK;IACjC,MAAMsB,aAAa,GAAGzB,QAAQ,CAAC0B,OAAO,CAACvB,EAAE,CAAC;IAC1C,IAAIkB,WAAW,GAAG,EAAE;IAEpB,IAAII,aAAa,KAAK,CAAC,CAAC,EAAE;MACxBJ,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAC3B,QAAQ,EAAEG,EAAE,CAAC;IAChD,CAAC,MAAM,IAAIsB,aAAa,KAAK,CAAC,EAAE;MAC9BJ,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAC3B,QAAQ,CAAC4B,KAAK,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM,IAAIH,aAAa,KAAKzB,QAAQ,CAAC6B,MAAM,GAAG,CAAC,EAAE;MAChDR,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAC3B,QAAQ,CAAC4B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC,MAAM,IAAIH,aAAa,GAAG,CAAC,EAAE;MAC5BJ,WAAW,GAAGA,WAAW,CAACM,MAAM,CAC9B3B,QAAQ,CAAC4B,KAAK,CAAC,CAAC,EAAEH,aAAa,CAAC,EAChCzB,QAAQ,CAAC4B,KAAK,CAACH,aAAa,GAAG,CAAC,CAClC,CAAC;IACH;IACAxB,WAAW,CAACoB,WAAW,CAAC;EAC1B,CAAC;EAED,MAAMS,UAAU,GAAI3B,EAAE,IAAKH,QAAQ,CAAC0B,OAAO,CAACvB,EAAE,CAAC,KAAK,CAAC,CAAC;EAEtD,MAAM4B,WAAW,GAAI3B,IAAI,IAAK;IAC5B,OAAOA,IAAI,CACR4B,KAAK,CAAC,GAAG,CAAC,CACVV,GAAG,CAACW,IAAI,IAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC3BC,IAAI,CAAC,EAAE,CAAC,CACRC,WAAW,CAAC,CAAC,CACbR,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EAED,MAAMS,cAAc,GAAIjC,IAAI,IAAK;IAC/B,MAAMkC,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC3C;IACD,MAAMC,KAAK,GAAGnC,IAAI,CAACoC,UAAU,CAAC,CAAC,CAAC,GAAGF,MAAM,CAACT,MAAM;IAChD,OAAOS,MAAM,CAACC,KAAK,CAAC;EACtB,CAAC;EAED,MAAME,cAAc,GAAIhC,MAAM,IAAK;IACjC,QAAQA,MAAM,CAACiC,WAAW,CAAC,CAAC;MAC1B,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMC,cAAc,GAAGzC,UAAU,CAAC0C,MAAM,CAACC,KAAK,IAC5CA,KAAK,CAACzC,IAAI,CAACsC,WAAW,CAAC,CAAC,CAACI,QAAQ,CAAChD,UAAU,CAAC4C,WAAW,CAAC,CAAC,CAAC,IAC3DG,KAAK,CAACvC,KAAK,CAACoC,WAAW,CAAC,CAAC,CAACI,QAAQ,CAAChD,UAAU,CAAC4C,WAAW,CAAC,CAAC,CAAC,IAC5DG,KAAK,CAACxC,KAAK,CAACyC,QAAQ,CAAChD,UAAU,CACjC,CAAC;EAED,MAAMiD,eAAe,GAAGJ,cAAc,CAACf,KAAK,CAC1ClC,IAAI,GAAGE,WAAW,EAClBF,IAAI,GAAGE,WAAW,GAAGA,WACvB,CAAC;EAED,oBACEL,OAAA,CAACzB,GAAG;IAACkF,EAAE,EAAE;MAAEC,eAAe,EAAE,SAAS;MAAEC,SAAS,EAAE,OAAO;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eACjE7D,OAAA,CAACxB,SAAS;MAACsF,QAAQ,EAAC,IAAI;MAAAD,QAAA,gBAEtB7D,OAAA,CAACtB,WAAW;QAAC,cAAW,YAAY;QAAC+E,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,gBACjD7D,OAAA,CAACrB,IAAI;UAACqF,SAAS,EAAC,OAAO;UAACC,KAAK,EAAC,SAAS;UAACC,IAAI,EAAC,GAAG;UAAAL,QAAA,EAAC;QAEjD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPtE,OAAA,CAACvB,UAAU;UAACwF,KAAK,EAAC,SAAS;UAAAJ,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAGdtE,OAAA,CAACzB,GAAG;QAACkF,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eACjB7D,OAAA,CAACb,SAAS;UACRoF,SAAS;UACTC,WAAW,EAAC,2BAA2B;UACvC9C,KAAK,EAAEnB,UAAW;UAClBkE,QAAQ,EAAE9C,kBAAmB;UAC7B+C,UAAU,EAAE;YACVC,cAAc,eACZ3E,OAAA,CAACZ,cAAc;cAACwF,QAAQ,EAAC,OAAO;cAAAf,QAAA,eAC9B7D,OAAA,CAACL,UAAU;gBAACsE,KAAK,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAEpB,CAAE;UACFb,EAAE,EAAE;YACFK,QAAQ,EAAE,GAAG;YACbJ,eAAe,EAAE,OAAO;YACxB,0BAA0B,EAAE;cAC1BmB,YAAY,EAAE;YAChB;UACF;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNtE,OAAA,CAACpB,KAAK;QAAC6E,EAAE,EAAE;UAAEqB,KAAK,EAAE,MAAM;UAAEf,EAAE,EAAE,CAAC;UAAEc,YAAY,EAAE;QAAE,CAAE;QAAAhB,QAAA,gBACnD7D,OAAA,CAAChB,cAAc;UAAA6E,QAAA,eACb7D,OAAA,CAACnB,KAAK;YAAC4E,EAAE,EAAE;cAAEsB,QAAQ,EAAE;YAAI,CAAE;YAAC,mBAAgB,YAAY;YAAAlB,QAAA,gBACxD7D,OAAA,CAACf,SAAS;cAAA4E,QAAA,eACR7D,OAAA,CAACd,QAAQ;gBAACuE,EAAE,EAAE;kBAAEC,eAAe,EAAE;gBAAU,CAAE;gBAAAG,QAAA,gBAC3C7D,OAAA,CAACjB,SAAS;kBAACiG,OAAO,EAAC,UAAU;kBAAAnB,QAAA,eAC3B7D,OAAA,CAACP,QAAQ;oBACPwE,KAAK,EAAC,SAAS;oBACfgB,aAAa,EAAExE,QAAQ,CAAC6B,MAAM,GAAG,CAAC,IAAI7B,QAAQ,CAAC6B,MAAM,GAAG3B,UAAU,CAAC2B,MAAO;oBAC1ET,OAAO,EAAElB,UAAU,CAAC2B,MAAM,GAAG,CAAC,IAAI7B,QAAQ,CAAC6B,MAAM,KAAK3B,UAAU,CAAC2B,MAAO;oBACxEmC,QAAQ,EAAE7C;kBAAqB;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZtE,OAAA,CAACjB,SAAS;kBAAC0E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7EtE,OAAA,CAACjB,SAAS;kBAAC0E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrFtE,OAAA,CAACjB,SAAS;kBAAC0E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9EtE,OAAA,CAACjB,SAAS;kBAAC0E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnFtE,OAAA,CAACjB,SAAS;kBAAC0E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrFtE,OAAA,CAACjB,SAAS;kBAAC0E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrFtE,OAAA,CAACjB,SAAS;kBAAC0E,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZtE,OAAA,CAAClB,SAAS;cAAA+E,QAAA,EACPL,eAAe,CAACzB,GAAG,CAAC,CAACuB,KAAK,EAAEN,KAAK,KAAK;gBACrC,MAAMmC,cAAc,GAAG5C,UAAU,CAACe,KAAK,CAAC1C,EAAE,CAAC;gBAC3C,MAAMwE,OAAO,GAAG,2BAA2BpC,KAAK,EAAE;gBAElD,oBACEhD,OAAA,CAACd,QAAQ;kBACPmG,KAAK;kBACLC,OAAO,EAAGjE,KAAK,IAAKY,WAAW,CAACZ,KAAK,EAAEiC,KAAK,CAAC1C,EAAE,CAAE;kBACjD2E,IAAI,EAAC,UAAU;kBACf,gBAAcJ,cAAe;kBAC7BK,QAAQ,EAAE,CAAC,CAAE;kBAEb/E,QAAQ,EAAE0E,cAAe;kBACzB1B,EAAE,EAAE;oBAAEgC,MAAM,EAAE;kBAAU,CAAE;kBAAA5B,QAAA,gBAE1B7D,OAAA,CAACjB,SAAS;oBAACiG,OAAO,EAAC,UAAU;oBAAAnB,QAAA,eAC3B7D,OAAA,CAACP,QAAQ;sBACPwE,KAAK,EAAC,SAAS;sBACfpC,OAAO,EAAEsD,cAAe;sBACxBO,UAAU,EAAE;wBACV,iBAAiB,EAAEN;sBACrB;oBAAE;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZtE,OAAA,CAACjB,SAAS;oBAAC4G,SAAS,EAAC,IAAI;oBAAC/E,EAAE,EAAEwE,OAAQ;oBAACQ,KAAK,EAAC,KAAK;oBAAA/B,QAAA,eAChD7D,OAAA,CAACzB,GAAG;sBAACkF,EAAE,EAAE;wBAAEoC,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,GAAG,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,gBACzD7D,OAAA,CAACR,MAAM;wBACLiE,EAAE,EAAE;0BACFuC,OAAO,EAAElD,cAAc,CAACQ,KAAK,CAACzC,IAAI,CAAC;0BACnCiE,KAAK,EAAE,EAAE;0BACTmB,MAAM,EAAE,EAAE;0BACVC,QAAQ,EAAE;wBACZ,CAAE;wBAAArC,QAAA,EAEDrB,WAAW,CAACc,KAAK,CAACzC,IAAI;sBAAC;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC,eACTtE,OAAA,CAACvB,UAAU;wBAAC0H,OAAO,EAAC,OAAO;wBAAC1C,EAAE,EAAE;0BAAEyB,UAAU,EAAE;wBAAI,CAAE;wBAAArB,QAAA,EACjDP,KAAK,CAACzC;sBAAI;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZtE,OAAA,CAACjB,SAAS;oBAAA8E,QAAA,eACR7D,OAAA,CAACvB,UAAU;sBAAC0H,OAAO,EAAC,OAAO;sBAAClC,KAAK,EAAC,SAAS;sBAACR,EAAE,EAAE;wBAAEyB,UAAU,EAAE;sBAAI,CAAE;sBAAArB,QAAA,EACjEP,KAAK,CAACxC;oBAAK;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZtE,OAAA,CAACjB,SAAS;oBAAA8E,QAAA,eACR7D,OAAA,CAACvB,UAAU;sBAAC0H,OAAO,EAAC,OAAO;sBAAAtC,QAAA,EACxBP,KAAK,CAACvC;oBAAK;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZtE,OAAA,CAACjB,SAAS;oBAAA8E,QAAA,eACR7D,OAAA,CAACX,IAAI;sBACH+G,KAAK,EAAE9C,KAAK,CAACrC,UAAW;sBACxBgD,KAAK,EAAEX,KAAK,CAACrC,UAAU,KAAK,KAAK,GAAG,SAAS,GAAG,OAAQ;sBACxDoF,IAAI,EAAC,OAAO;sBACZ5C,EAAE,EAAE;wBAAEsB,QAAQ,EAAE;sBAAG;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZtE,OAAA,CAACjB,SAAS;oBAAA8E,QAAA,eACR7D,OAAA,CAACX,IAAI;sBACH+G,KAAK,EAAE9C,KAAK,CAACnC,WAAY;sBACzB8C,KAAK,EAAEf,cAAc,CAACI,KAAK,CAACnC,WAAW,CAAE;sBACzCkF,IAAI,EAAC,OAAO;sBACZ5C,EAAE,EAAE;wBAAEsB,QAAQ,EAAE;sBAAG;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZtE,OAAA,CAACjB,SAAS;oBAAA8E,QAAA,eACR7D,OAAA,CAACvB,UAAU;sBAAC0H,OAAO,EAAC,OAAO;sBAAAtC,QAAA,EACxBP,KAAK,CAACtC;oBAAQ;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZtE,OAAA,CAACjB,SAAS;oBAAA8E,QAAA,eACR7D,OAAA,CAACzB,GAAG;sBAACkF,EAAE,EAAE;wBAAEoC,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,GAAG,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,gBACzD7D,OAAA,CAACX,IAAI;wBACH+G,KAAK,EAAE9C,KAAK,CAACpC,MAAO;wBACpB+C,KAAK,EAAEf,cAAc,CAACI,KAAK,CAACpC,MAAM,CAAE;wBACpCmF,IAAI,EAAC,OAAO;wBACZ5C,EAAE,EAAE;0BAAEsB,QAAQ,EAAE;wBAAG;sBAAE;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eACFtE,OAAA,CAACV,UAAU;wBAAC+G,IAAI,EAAC,OAAO;wBAACpC,KAAK,EAAC,SAAS;wBAAAJ,QAAA,eACtC7D,OAAA,CAACH,QAAQ;0BAACqG,QAAQ,EAAC;wBAAO;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAzEPhB,KAAK,CAAC1C,EAAE;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0EL,CAAC;cAEf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGjBtE,OAAA,CAACzB,GAAG;UAACkF,EAAE,EAAE;YAAEoC,OAAO,EAAE,MAAM;YAAES,cAAc,EAAE,eAAe;YAAER,UAAU,EAAE,QAAQ;YAAES,CAAC,EAAE;UAAE,CAAE;UAAA1C,QAAA,gBACxF7D,OAAA,CAACvB,UAAU;YAAC0H,OAAO,EAAC,OAAO;YAAClC,KAAK,EAAC,gBAAgB;YAAAJ,QAAA,GAAC,UACzC,EAAC1D,IAAI,GAAGE,WAAW,GAAG,CAAC,EAAC,MAAI,EAACmG,IAAI,CAACC,GAAG,CAAC,CAACtG,IAAI,GAAG,CAAC,IAAIE,WAAW,EAAE+C,cAAc,CAACd,MAAM,CAAC,EAAC,MAAI,EAACc,cAAc,CAACd,MAAM;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC,eACbtE,OAAA,CAACT,eAAe;YACdmH,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAE;YAChCf,SAAS,EAAC,KAAK;YACfgB,KAAK,EAAEvD,cAAc,CAACd,MAAO;YAC7BjC,WAAW,EAAEA,WAAY;YACzBF,IAAI,EAAEA,IAAK;YACXyG,YAAY,EAAExF,gBAAiB;YAC/ByF,mBAAmB,EAAEtF,uBAAwB;YAC7CkC,EAAE,EAAE;cACF,+BAA+B,EAAE;gBAC/BE,SAAS,EAAE;cACb,CAAC;cACD,wEAAwE,EAAE;gBACxEmD,MAAM,EAAE;cACV;YACF;UAAE;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACpE,EAAA,CAtXID,SAAS;AAAA8G,EAAA,GAAT9G,SAAS;AAwXf,eAAeA,SAAS;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}