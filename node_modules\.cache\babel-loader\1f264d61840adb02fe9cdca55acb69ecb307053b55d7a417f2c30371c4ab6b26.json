{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Dash\\\\src\\\\components\\\\AllAgents.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Container, Typography, Breadcrumbs, Link, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, InputAdornment, Chip, IconButton, TablePagination, Avatar, Checkbox } from '@mui/material';\nimport { Search as SearchIcon, Edit as EditIcon } from '@mui/icons-material';\nimport EditAgentModal from './EditAgentModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllAgents = () => {\n  _s();\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selected, setSelected] = useState([]);\n  const [editModalOpen, setEditModalOpen] = useState(false);\n  const [selectedAgent, setSelectedAgent] = useState(null);\n\n  // Sample agent data\n  const agentsData = [{\n    id: 1,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified',\n    floridaLicense: 'FL123456789'\n  }, {\n    id: 2,\n    name: 'Shafin Desai',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 10, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified',\n    floridaLicense: 'FL987654321'\n  }, {\n    id: 3,\n    name: 'Michael Johnson',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 08, 2025',\n    commission: 'No',\n    status: 'Pending',\n    runningLead: 'Pending',\n    floridaLicense: 'FL456789123'\n  }, {\n    id: 4,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 5,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 6,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 7,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 8,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'Yes',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 9,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'No',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }, {\n    id: 10,\n    name: 'Jessica Morgan',\n    phone: '(*************',\n    email: '<EMAIL>',\n    joinDate: 'Jan 12, 2025',\n    commission: 'No',\n    status: 'Verified',\n    runningLead: 'Verified'\n  }];\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n  const handleChangeRowsPerPage = event => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n  const handleSearchChange = event => {\n    setSearchTerm(event.target.value);\n  };\n  const handleSelectAllClick = event => {\n    if (event.target.checked) {\n      const newSelected = agentsData.map(n => n.id);\n      setSelected(newSelected);\n      return;\n    }\n    setSelected([]);\n  };\n  const handleClick = (event, id) => {\n    const selectedIndex = selected.indexOf(id);\n    let newSelected = [];\n    if (selectedIndex === -1) {\n      newSelected = newSelected.concat(selected, id);\n    } else if (selectedIndex === 0) {\n      newSelected = newSelected.concat(selected.slice(1));\n    } else if (selectedIndex === selected.length - 1) {\n      newSelected = newSelected.concat(selected.slice(0, -1));\n    } else if (selectedIndex > 0) {\n      newSelected = newSelected.concat(selected.slice(0, selectedIndex), selected.slice(selectedIndex + 1));\n    }\n    setSelected(newSelected);\n  };\n  const isSelected = id => selected.indexOf(id) !== -1;\n  const handleEditAgent = agent => {\n    setSelectedAgent(agent);\n    setEditModalOpen(true);\n  };\n  const handleCloseEditModal = () => {\n    setEditModalOpen(false);\n    setSelectedAgent(null);\n  };\n  const handleSaveAgent = updatedAgentData => {\n    // Here you would typically update the agent data in your backend/state\n    console.log('Saving agent data:', updatedAgentData);\n    // For now, just log the data - you can implement the actual save logic later\n  };\n  const getInitials = name => {\n    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);\n  };\n  const getAvatarColor = name => {\n    const colors = ['#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4caf50', '#8bc34a', '#cddc39'];\n    const index = name.charCodeAt(0) % colors.length;\n    return colors[index];\n  };\n  const getStatusColor = status => {\n    switch (status.toLowerCase()) {\n      case 'verified':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'rejected':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const filteredAgents = agentsData.filter(agent => agent.name.toLowerCase().includes(searchTerm.toLowerCase()) || agent.email.toLowerCase().includes(searchTerm.toLowerCase()) || agent.phone.includes(searchTerm));\n  const paginatedAgents = filteredAgents.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      backgroundColor: '#f5f5f5',\n      minHeight: '100vh',\n      py: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: [/*#__PURE__*/_jsxDEV(Breadcrumbs, {\n        \"aria-label\": \"breadcrumb\",\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          underline: \"hover\",\n          color: \"inherit\",\n          href: \"/\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"primary\",\n          children: \"All Agents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Search by username, email\",\n          value: searchTerm,\n          onChange: handleSearchChange,\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                color: \"action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this)\n          },\n          sx: {\n            maxWidth: 400,\n            backgroundColor: 'white',\n            '& .MuiOutlinedInput-root': {\n              borderRadius: 2\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          width: '100%',\n          mb: 2,\n          borderRadius: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            sx: {\n              minWidth: 750\n            },\n            \"aria-labelledby\": \"tableTitle\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  backgroundColor: '#f8f9fa'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  padding: \"checkbox\",\n                  children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                    color: \"primary\",\n                    indeterminate: selected.length > 0 && selected.length < agentsData.length,\n                    checked: agentsData.length > 0 && selected.length === agentsData.length,\n                    onChange: handleSelectAllClick\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Phone Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Commission\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Running Lead\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Joining Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: paginatedAgents.map((agent, index) => {\n                const isItemSelected = isSelected(agent.id);\n                const labelId = `enhanced-table-checkbox-${index}`;\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  hover: true,\n                  onClick: event => handleClick(event, agent.id),\n                  role: \"checkbox\",\n                  \"aria-checked\": isItemSelected,\n                  tabIndex: -1,\n                  selected: isItemSelected,\n                  sx: {\n                    cursor: 'pointer'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    padding: \"checkbox\",\n                    children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                      color: \"primary\",\n                      checked: isItemSelected,\n                      inputProps: {\n                        'aria-labelledby': labelId\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    component: \"th\",\n                    id: labelId,\n                    scope: \"row\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                        sx: {\n                          bgcolor: getAvatarColor(agent.name),\n                          width: 32,\n                          height: 32,\n                          fontSize: '0.875rem'\n                        },\n                        children: getInitials(agent.name)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 331,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 500\n                        },\n                        children: agent.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 341,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"primary\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: agent.phone\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: agent.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: agent.commission,\n                      color: agent.commission === 'Yes' ? 'success' : 'error',\n                      size: \"small\",\n                      sx: {\n                        minWidth: 60\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: agent.runningLead,\n                      color: getStatusColor(agent.runningLead),\n                      size: \"small\",\n                      sx: {\n                        minWidth: 80\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: agent.joinDate\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Chip, {\n                        label: agent.status,\n                        color: getStatusColor(agent.status),\n                        size: \"small\",\n                        sx: {\n                          minWidth: 80\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 379,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"primary\",\n                        onClick: () => handleEditAgent(agent),\n                        children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 390,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 385,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 23\n                  }, this)]\n                }, agent.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"Showing \", page * rowsPerPage + 1, \" to \", Math.min((page + 1) * rowsPerPage, filteredAgents.length), \" of \", filteredAgents.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n            rowsPerPageOptions: [5, 10, 25],\n            component: \"div\",\n            count: filteredAgents.length,\n            rowsPerPage: rowsPerPage,\n            page: page,\n            onPageChange: handleChangePage,\n            onRowsPerPageChange: handleChangeRowsPerPage,\n            sx: {\n              '& .MuiTablePagination-toolbar': {\n                minHeight: 'auto'\n              },\n              '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {\n                margin: 0\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditAgentModal, {\n      open: editModalOpen,\n      onClose: handleCloseEditModal,\n      agent: selectedAgent,\n      onSave: handleSaveAgent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n};\n_s(AllAgents, \"Jybuvw6V2h6Q2rZWORTqnvKxXFI=\");\n_c = AllAgents;\nexport default AllAgents;\nvar _c;\n$RefreshReg$(_c, \"AllAgents\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Container", "Typography", "Breadcrumbs", "Link", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TextField", "InputAdornment", "Chip", "IconButton", "TablePagination", "Avatar", "Checkbox", "Search", "SearchIcon", "Edit", "EditIcon", "EditAgentModal", "jsxDEV", "_jsxDEV", "AllAgents", "_s", "page", "setPage", "rowsPerPage", "setRowsPerPage", "searchTerm", "setSearchTerm", "selected", "setSelected", "editModalOpen", "setEditModalOpen", "selectedAgent", "setSelectedAgent", "agentsData", "id", "name", "phone", "email", "joinDate", "commission", "status", "runningLead", "floridaLicense", "handleChangePage", "event", "newPage", "handleChangeRowsPerPage", "parseInt", "target", "value", "handleSearchChange", "handleSelectAllClick", "checked", "newSelected", "map", "n", "handleClick", "selectedIndex", "indexOf", "concat", "slice", "length", "isSelected", "handleEditAgent", "agent", "handleCloseEditModal", "handleSaveAgent", "updatedAgentData", "console", "log", "getInitials", "split", "word", "char<PERSON>t", "join", "toUpperCase", "getAvatarColor", "colors", "index", "charCodeAt", "getStatusColor", "toLowerCase", "filteredAgents", "filter", "includes", "paginatedAgents", "sx", "backgroundColor", "minHeight", "py", "children", "max<PERSON><PERSON><PERSON>", "mb", "underline", "color", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "placeholder", "onChange", "InputProps", "startAdornment", "position", "borderRadius", "width", "min<PERSON><PERSON><PERSON>", "padding", "indeterminate", "fontWeight", "isItemSelected", "labelId", "hover", "onClick", "role", "tabIndex", "cursor", "inputProps", "component", "scope", "display", "alignItems", "gap", "bgcolor", "height", "fontSize", "variant", "label", "size", "justifyContent", "p", "Math", "min", "rowsPerPageOptions", "count", "onPageChange", "onRowsPerPageChange", "margin", "open", "onClose", "onSave", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Dash/src/components/AllAgents.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Breadcrumbs,\n  Link,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TextField,\n  InputAdornment,\n  Chip,\n  IconButton,\n  TablePagination,\n  Avatar,\n  Checkbox,\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Edit as EditIcon,\n} from '@mui/icons-material';\nimport EditAgentModal from './EditAgentModal';\n\nconst AllAgents = () => {\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selected, setSelected] = useState([]);\n  const [editModalOpen, setEditModalOpen] = useState(false);\n  const [selectedAgent, setSelectedAgent] = useState(null);\n\n  // Sample agent data\n  const agentsData = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n      floridaLicense: 'FL123456789',\n    },\n    {\n      id: 2,\n      name: 'Shafin Desai',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 10, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n      floridaLicense: 'FL987654321',\n    },\n    {\n      id: 3,\n      name: 'Michael Johnson',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 08, 2025',\n      commission: 'No',\n      status: 'Pending',\n      runningLead: 'Pending',\n      floridaLicense: 'FL456789123',\n    },\n    {\n      id: 4,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 5,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 6,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 7,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 8,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'Yes',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 9,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'No',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n    {\n      id: 10,\n      name: 'Jessica Morgan',\n      phone: '(*************',\n      email: '<EMAIL>',\n      joinDate: 'Jan 12, 2025',\n      commission: 'No',\n      status: 'Verified',\n      runningLead: 'Verified',\n    },\n  ];\n\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n\n  const handleChangeRowsPerPage = (event) => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n\n  const handleSearchChange = (event) => {\n    setSearchTerm(event.target.value);\n  };\n\n  const handleSelectAllClick = (event) => {\n    if (event.target.checked) {\n      const newSelected = agentsData.map((n) => n.id);\n      setSelected(newSelected);\n      return;\n    }\n    setSelected([]);\n  };\n\n  const handleClick = (event, id) => {\n    const selectedIndex = selected.indexOf(id);\n    let newSelected = [];\n\n    if (selectedIndex === -1) {\n      newSelected = newSelected.concat(selected, id);\n    } else if (selectedIndex === 0) {\n      newSelected = newSelected.concat(selected.slice(1));\n    } else if (selectedIndex === selected.length - 1) {\n      newSelected = newSelected.concat(selected.slice(0, -1));\n    } else if (selectedIndex > 0) {\n      newSelected = newSelected.concat(\n        selected.slice(0, selectedIndex),\n        selected.slice(selectedIndex + 1),\n      );\n    }\n    setSelected(newSelected);\n  };\n\n  const isSelected = (id) => selected.indexOf(id) !== -1;\n\n  const handleEditAgent = (agent) => {\n    setSelectedAgent(agent);\n    setEditModalOpen(true);\n  };\n\n  const handleCloseEditModal = () => {\n    setEditModalOpen(false);\n    setSelectedAgent(null);\n  };\n\n  const handleSaveAgent = (updatedAgentData) => {\n    // Here you would typically update the agent data in your backend/state\n    console.log('Saving agent data:', updatedAgentData);\n    // For now, just log the data - you can implement the actual save logic later\n  };\n\n  const getInitials = (name) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  const getAvatarColor = (name) => {\n    const colors = [\n      '#f44336', '#e91e63', '#9c27b0', '#673ab7',\n      '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4',\n      '#009688', '#4caf50', '#8bc34a', '#cddc39',\n    ];\n    const index = name.charCodeAt(0) % colors.length;\n    return colors[index];\n  };\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'verified':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'rejected':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const filteredAgents = agentsData.filter(agent =>\n    agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    agent.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    agent.phone.includes(searchTerm)\n  );\n\n  const paginatedAgents = filteredAgents.slice(\n    page * rowsPerPage,\n    page * rowsPerPage + rowsPerPage\n  );\n\n  return (\n    <Box sx={{ backgroundColor: '#f5f5f5', minHeight: '100vh', py: 3 }}>\n      <Container maxWidth=\"xl\">\n        {/* Breadcrumbs */}\n        <Breadcrumbs aria-label=\"breadcrumb\" sx={{ mb: 3 }}>\n          <Link underline=\"hover\" color=\"inherit\" href=\"/\">\n            Dashboard\n          </Link>\n          <Typography color=\"primary\">All Agents</Typography>\n        </Breadcrumbs>\n\n        {/* Search Bar */}\n        <Box sx={{ mb: 3 }}>\n          <TextField\n            fullWidth\n            placeholder=\"Search by username, email\"\n            value={searchTerm}\n            onChange={handleSearchChange}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon color=\"action\" />\n                </InputAdornment>\n              ),\n            }}\n            sx={{\n              maxWidth: 400,\n              backgroundColor: 'white',\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 2,\n              },\n            }}\n          />\n        </Box>\n\n        {/* Table */}\n        <Paper sx={{ width: '100%', mb: 2, borderRadius: 2 }}>\n          <TableContainer>\n            <Table sx={{ minWidth: 750 }} aria-labelledby=\"tableTitle\">\n              <TableHead>\n                <TableRow sx={{ backgroundColor: '#f8f9fa' }}>\n                  <TableCell padding=\"checkbox\">\n                    <Checkbox\n                      color=\"primary\"\n                      indeterminate={selected.length > 0 && selected.length < agentsData.length}\n                      checked={agentsData.length > 0 && selected.length === agentsData.length}\n                      onChange={handleSelectAllClick}\n                    />\n                  </TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Name</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Phone Number</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Email</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Commission</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Running Lead</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Joining Date</TableCell>\n                  <TableCell sx={{ fontWeight: 600, color: 'text.secondary' }}>Status</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {paginatedAgents.map((agent, index) => {\n                  const isItemSelected = isSelected(agent.id);\n                  const labelId = `enhanced-table-checkbox-${index}`;\n\n                  return (\n                    <TableRow\n                      hover\n                      onClick={(event) => handleClick(event, agent.id)}\n                      role=\"checkbox\"\n                      aria-checked={isItemSelected}\n                      tabIndex={-1}\n                      key={agent.id}\n                      selected={isItemSelected}\n                      sx={{ cursor: 'pointer' }}\n                    >\n                      <TableCell padding=\"checkbox\">\n                        <Checkbox\n                          color=\"primary\"\n                          checked={isItemSelected}\n                          inputProps={{\n                            'aria-labelledby': labelId,\n                          }}\n                        />\n                      </TableCell>\n                      <TableCell component=\"th\" id={labelId} scope=\"row\">\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                          <Avatar\n                            sx={{\n                              bgcolor: getAvatarColor(agent.name),\n                              width: 32,\n                              height: 32,\n                              fontSize: '0.875rem',\n                            }}\n                          >\n                            {getInitials(agent.name)}\n                          </Avatar>\n                          <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                            {agent.name}\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\" color=\"primary\" sx={{ fontWeight: 500 }}>\n                          {agent.phone}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {agent.email}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={agent.commission}\n                          color={agent.commission === 'Yes' ? 'success' : 'error'}\n                          size=\"small\"\n                          sx={{ minWidth: 60 }}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={agent.runningLead}\n                          color={getStatusColor(agent.runningLead)}\n                          size=\"small\"\n                          sx={{ minWidth: 80 }}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {agent.joinDate}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          <Chip\n                            label={agent.status}\n                            color={getStatusColor(agent.status)}\n                            size=\"small\"\n                            sx={{ minWidth: 80 }}\n                          />\n                          <IconButton\n                            size=\"small\"\n                            color=\"primary\"\n                            onClick={() => handleEditAgent(agent)}\n                          >\n                            <EditIcon fontSize=\"small\" />\n                          </IconButton>\n                        </Box>\n                      </TableCell>\n                    </TableRow>\n                  );\n                })}\n              </TableBody>\n            </Table>\n          </TableContainer>\n          \n          {/* Pagination */}\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Showing {page * rowsPerPage + 1} to {Math.min((page + 1) * rowsPerPage, filteredAgents.length)} of {filteredAgents.length}\n            </Typography>\n            <TablePagination\n              rowsPerPageOptions={[5, 10, 25]}\n              component=\"div\"\n              count={filteredAgents.length}\n              rowsPerPage={rowsPerPage}\n              page={page}\n              onPageChange={handleChangePage}\n              onRowsPerPageChange={handleChangeRowsPerPage}\n              sx={{\n                '& .MuiTablePagination-toolbar': {\n                  minHeight: 'auto',\n                },\n                '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {\n                  margin: 0,\n                },\n              }}\n            />\n          </Box>\n        </Paper>\n      </Container>\n\n      {/* Edit Agent Modal */}\n      <EditAgentModal\n        open={editModalOpen}\n        onClose={handleCloseEditModal}\n        agent={selectedAgent}\n        onSave={handleSaveAgent}\n      />\n    </Box>\n  );\n};\n\nexport default AllAgents;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,eAAe,EACfC,MAAM,EACNC,QAAQ,QACH,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAMyC,UAAU,GAAG,CACjB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE,UAAU;IACvBC,cAAc,EAAE;EAClB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,wBAAwB;IAC/BC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE,UAAU;IACvBC,cAAc,EAAE;EAClB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,2BAA2B;IAClCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,cAAc,EAAE;EAClB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,EAAE,EAAE,EAAE;IACNC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAME,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC3CvB,OAAO,CAACuB,OAAO,CAAC;EAClB,CAAC;EAED,MAAMC,uBAAuB,GAAIF,KAAK,IAAK;IACzCpB,cAAc,CAACuB,QAAQ,CAACH,KAAK,CAACI,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,CAAC;IAChD3B,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC;EAED,MAAM4B,kBAAkB,GAAIN,KAAK,IAAK;IACpClB,aAAa,CAACkB,KAAK,CAACI,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;EAED,MAAME,oBAAoB,GAAIP,KAAK,IAAK;IACtC,IAAIA,KAAK,CAACI,MAAM,CAACI,OAAO,EAAE;MACxB,MAAMC,WAAW,GAAGpB,UAAU,CAACqB,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACrB,EAAE,CAAC;MAC/CN,WAAW,CAACyB,WAAW,CAAC;MACxB;IACF;IACAzB,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EAED,MAAM4B,WAAW,GAAGA,CAACZ,KAAK,EAAEV,EAAE,KAAK;IACjC,MAAMuB,aAAa,GAAG9B,QAAQ,CAAC+B,OAAO,CAACxB,EAAE,CAAC;IAC1C,IAAImB,WAAW,GAAG,EAAE;IAEpB,IAAII,aAAa,KAAK,CAAC,CAAC,EAAE;MACxBJ,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAChC,QAAQ,EAAEO,EAAE,CAAC;IAChD,CAAC,MAAM,IAAIuB,aAAa,KAAK,CAAC,EAAE;MAC9BJ,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAChC,QAAQ,CAACiC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM,IAAIH,aAAa,KAAK9B,QAAQ,CAACkC,MAAM,GAAG,CAAC,EAAE;MAChDR,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAChC,QAAQ,CAACiC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC,MAAM,IAAIH,aAAa,GAAG,CAAC,EAAE;MAC5BJ,WAAW,GAAGA,WAAW,CAACM,MAAM,CAC9BhC,QAAQ,CAACiC,KAAK,CAAC,CAAC,EAAEH,aAAa,CAAC,EAChC9B,QAAQ,CAACiC,KAAK,CAACH,aAAa,GAAG,CAAC,CAClC,CAAC;IACH;IACA7B,WAAW,CAACyB,WAAW,CAAC;EAC1B,CAAC;EAED,MAAMS,UAAU,GAAI5B,EAAE,IAAKP,QAAQ,CAAC+B,OAAO,CAACxB,EAAE,CAAC,KAAK,CAAC,CAAC;EAEtD,MAAM6B,eAAe,GAAIC,KAAK,IAAK;IACjChC,gBAAgB,CAACgC,KAAK,CAAC;IACvBlC,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMmC,oBAAoB,GAAGA,CAAA,KAAM;IACjCnC,gBAAgB,CAAC,KAAK,CAAC;IACvBE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMkC,eAAe,GAAIC,gBAAgB,IAAK;IAC5C;IACAC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,gBAAgB,CAAC;IACnD;EACF,CAAC;EAED,MAAMG,WAAW,GAAInC,IAAI,IAAK;IAC5B,OAAOA,IAAI,CACRoC,KAAK,CAAC,GAAG,CAAC,CACVjB,GAAG,CAACkB,IAAI,IAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC3BC,IAAI,CAAC,EAAE,CAAC,CACRC,WAAW,CAAC,CAAC,CACbf,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EAED,MAAMgB,cAAc,GAAIzC,IAAI,IAAK;IAC/B,MAAM0C,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC3C;IACD,MAAMC,KAAK,GAAG3C,IAAI,CAAC4C,UAAU,CAAC,CAAC,CAAC,GAAGF,MAAM,CAAChB,MAAM;IAChD,OAAOgB,MAAM,CAACC,KAAK,CAAC;EACtB,CAAC;EAED,MAAME,cAAc,GAAIxC,MAAM,IAAK;IACjC,QAAQA,MAAM,CAACyC,WAAW,CAAC,CAAC;MAC1B,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMC,cAAc,GAAGjD,UAAU,CAACkD,MAAM,CAACnB,KAAK,IAC5CA,KAAK,CAAC7B,IAAI,CAAC8C,WAAW,CAAC,CAAC,CAACG,QAAQ,CAAC3D,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC,IAC3DjB,KAAK,CAAC3B,KAAK,CAAC4C,WAAW,CAAC,CAAC,CAACG,QAAQ,CAAC3D,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC,IAC5DjB,KAAK,CAAC5B,KAAK,CAACgD,QAAQ,CAAC3D,UAAU,CACjC,CAAC;EAED,MAAM4D,eAAe,GAAGH,cAAc,CAACtB,KAAK,CAC1CvC,IAAI,GAAGE,WAAW,EAClBF,IAAI,GAAGE,WAAW,GAAGA,WACvB,CAAC;EAED,oBACEL,OAAA,CAACzB,GAAG;IAAC6F,EAAE,EAAE;MAAEC,eAAe,EAAE,SAAS;MAAEC,SAAS,EAAE,OAAO;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACjExE,OAAA,CAACxB,SAAS;MAACiG,QAAQ,EAAC,IAAI;MAAAD,QAAA,gBAEtBxE,OAAA,CAACtB,WAAW;QAAC,cAAW,YAAY;QAAC0F,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,gBACjDxE,OAAA,CAACrB,IAAI;UAACgG,SAAS,EAAC,OAAO;UAACC,KAAK,EAAC,SAAS;UAACC,IAAI,EAAC,GAAG;UAAAL,QAAA,EAAC;QAEjD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPjF,OAAA,CAACvB,UAAU;UAACmG,KAAK,EAAC,SAAS;UAAAJ,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAGdjF,OAAA,CAACzB,GAAG;QAAC6F,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eACjBxE,OAAA,CAACb,SAAS;UACR+F,SAAS;UACTC,WAAW,EAAC,2BAA2B;UACvCpD,KAAK,EAAExB,UAAW;UAClB6E,QAAQ,EAAEpD,kBAAmB;UAC7BqD,UAAU,EAAE;YACVC,cAAc,eACZtF,OAAA,CAACZ,cAAc;cAACmG,QAAQ,EAAC,OAAO;cAAAf,QAAA,eAC9BxE,OAAA,CAACL,UAAU;gBAACiF,KAAK,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAEpB,CAAE;UACFb,EAAE,EAAE;YACFK,QAAQ,EAAE,GAAG;YACbJ,eAAe,EAAE,OAAO;YACxB,0BAA0B,EAAE;cAC1BmB,YAAY,EAAE;YAChB;UACF;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNjF,OAAA,CAACpB,KAAK;QAACwF,EAAE,EAAE;UAAEqB,KAAK,EAAE,MAAM;UAAEf,EAAE,EAAE,CAAC;UAAEc,YAAY,EAAE;QAAE,CAAE;QAAAhB,QAAA,gBACnDxE,OAAA,CAAChB,cAAc;UAAAwF,QAAA,eACbxE,OAAA,CAACnB,KAAK;YAACuF,EAAE,EAAE;cAAEsB,QAAQ,EAAE;YAAI,CAAE;YAAC,mBAAgB,YAAY;YAAAlB,QAAA,gBACxDxE,OAAA,CAACf,SAAS;cAAAuF,QAAA,eACRxE,OAAA,CAACd,QAAQ;gBAACkF,EAAE,EAAE;kBAAEC,eAAe,EAAE;gBAAU,CAAE;gBAAAG,QAAA,gBAC3CxE,OAAA,CAACjB,SAAS;kBAAC4G,OAAO,EAAC,UAAU;kBAAAnB,QAAA,eAC3BxE,OAAA,CAACP,QAAQ;oBACPmF,KAAK,EAAC,SAAS;oBACfgB,aAAa,EAAEnF,QAAQ,CAACkC,MAAM,GAAG,CAAC,IAAIlC,QAAQ,CAACkC,MAAM,GAAG5B,UAAU,CAAC4B,MAAO;oBAC1ET,OAAO,EAAEnB,UAAU,CAAC4B,MAAM,GAAG,CAAC,IAAIlC,QAAQ,CAACkC,MAAM,KAAK5B,UAAU,CAAC4B,MAAO;oBACxEyC,QAAQ,EAAEnD;kBAAqB;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZjF,OAAA,CAACjB,SAAS;kBAACqF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7EjF,OAAA,CAACjB,SAAS;kBAACqF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrFjF,OAAA,CAACjB,SAAS;kBAACqF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9EjF,OAAA,CAACjB,SAAS;kBAACqF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnFjF,OAAA,CAACjB,SAAS;kBAACqF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrFjF,OAAA,CAACjB,SAAS;kBAACqF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrFjF,OAAA,CAACjB,SAAS;kBAACqF,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEjB,KAAK,EAAE;kBAAiB,CAAE;kBAAAJ,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZjF,OAAA,CAAClB,SAAS;cAAA0F,QAAA,EACPL,eAAe,CAAC/B,GAAG,CAAC,CAACU,KAAK,EAAEc,KAAK,KAAK;gBACrC,MAAMkC,cAAc,GAAGlD,UAAU,CAACE,KAAK,CAAC9B,EAAE,CAAC;gBAC3C,MAAM+E,OAAO,GAAG,2BAA2BnC,KAAK,EAAE;gBAElD,oBACE5D,OAAA,CAACd,QAAQ;kBACP8G,KAAK;kBACLC,OAAO,EAAGvE,KAAK,IAAKY,WAAW,CAACZ,KAAK,EAAEoB,KAAK,CAAC9B,EAAE,CAAE;kBACjDkF,IAAI,EAAC,UAAU;kBACf,gBAAcJ,cAAe;kBAC7BK,QAAQ,EAAE,CAAC,CAAE;kBAEb1F,QAAQ,EAAEqF,cAAe;kBACzB1B,EAAE,EAAE;oBAAEgC,MAAM,EAAE;kBAAU,CAAE;kBAAA5B,QAAA,gBAE1BxE,OAAA,CAACjB,SAAS;oBAAC4G,OAAO,EAAC,UAAU;oBAAAnB,QAAA,eAC3BxE,OAAA,CAACP,QAAQ;sBACPmF,KAAK,EAAC,SAAS;sBACf1C,OAAO,EAAE4D,cAAe;sBACxBO,UAAU,EAAE;wBACV,iBAAiB,EAAEN;sBACrB;oBAAE;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZjF,OAAA,CAACjB,SAAS;oBAACuH,SAAS,EAAC,IAAI;oBAACtF,EAAE,EAAE+E,OAAQ;oBAACQ,KAAK,EAAC,KAAK;oBAAA/B,QAAA,eAChDxE,OAAA,CAACzB,GAAG;sBAAC6F,EAAE,EAAE;wBAAEoC,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,GAAG,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,gBACzDxE,OAAA,CAACR,MAAM;wBACL4E,EAAE,EAAE;0BACFuC,OAAO,EAAEjD,cAAc,CAACZ,KAAK,CAAC7B,IAAI,CAAC;0BACnCwE,KAAK,EAAE,EAAE;0BACTmB,MAAM,EAAE,EAAE;0BACVC,QAAQ,EAAE;wBACZ,CAAE;wBAAArC,QAAA,EAEDpB,WAAW,CAACN,KAAK,CAAC7B,IAAI;sBAAC;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC,eACTjF,OAAA,CAACvB,UAAU;wBAACqI,OAAO,EAAC,OAAO;wBAAC1C,EAAE,EAAE;0BAAEyB,UAAU,EAAE;wBAAI,CAAE;wBAAArB,QAAA,EACjD1B,KAAK,CAAC7B;sBAAI;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZjF,OAAA,CAACjB,SAAS;oBAAAyF,QAAA,eACRxE,OAAA,CAACvB,UAAU;sBAACqI,OAAO,EAAC,OAAO;sBAAClC,KAAK,EAAC,SAAS;sBAACR,EAAE,EAAE;wBAAEyB,UAAU,EAAE;sBAAI,CAAE;sBAAArB,QAAA,EACjE1B,KAAK,CAAC5B;oBAAK;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZjF,OAAA,CAACjB,SAAS;oBAAAyF,QAAA,eACRxE,OAAA,CAACvB,UAAU;sBAACqI,OAAO,EAAC,OAAO;sBAAAtC,QAAA,EACxB1B,KAAK,CAAC3B;oBAAK;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZjF,OAAA,CAACjB,SAAS;oBAAAyF,QAAA,eACRxE,OAAA,CAACX,IAAI;sBACH0H,KAAK,EAAEjE,KAAK,CAACzB,UAAW;sBACxBuD,KAAK,EAAE9B,KAAK,CAACzB,UAAU,KAAK,KAAK,GAAG,SAAS,GAAG,OAAQ;sBACxD2F,IAAI,EAAC,OAAO;sBACZ5C,EAAE,EAAE;wBAAEsB,QAAQ,EAAE;sBAAG;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZjF,OAAA,CAACjB,SAAS;oBAAAyF,QAAA,eACRxE,OAAA,CAACX,IAAI;sBACH0H,KAAK,EAAEjE,KAAK,CAACvB,WAAY;sBACzBqD,KAAK,EAAEd,cAAc,CAAChB,KAAK,CAACvB,WAAW,CAAE;sBACzCyF,IAAI,EAAC,OAAO;sBACZ5C,EAAE,EAAE;wBAAEsB,QAAQ,EAAE;sBAAG;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZjF,OAAA,CAACjB,SAAS;oBAAAyF,QAAA,eACRxE,OAAA,CAACvB,UAAU;sBAACqI,OAAO,EAAC,OAAO;sBAAAtC,QAAA,EACxB1B,KAAK,CAAC1B;oBAAQ;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZjF,OAAA,CAACjB,SAAS;oBAAAyF,QAAA,eACRxE,OAAA,CAACzB,GAAG;sBAAC6F,EAAE,EAAE;wBAAEoC,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,GAAG,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,gBACzDxE,OAAA,CAACX,IAAI;wBACH0H,KAAK,EAAEjE,KAAK,CAACxB,MAAO;wBACpBsD,KAAK,EAAEd,cAAc,CAAChB,KAAK,CAACxB,MAAM,CAAE;wBACpC0F,IAAI,EAAC,OAAO;wBACZ5C,EAAE,EAAE;0BAAEsB,QAAQ,EAAE;wBAAG;sBAAE;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eACFjF,OAAA,CAACV,UAAU;wBACT0H,IAAI,EAAC,OAAO;wBACZpC,KAAK,EAAC,SAAS;wBACfqB,OAAO,EAAEA,CAAA,KAAMpD,eAAe,CAACC,KAAK,CAAE;wBAAA0B,QAAA,eAEtCxE,OAAA,CAACH,QAAQ;0BAACgH,QAAQ,EAAC;wBAAO;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GA7EPnC,KAAK,CAAC9B,EAAE;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8EL,CAAC;cAEf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGjBjF,OAAA,CAACzB,GAAG;UAAC6F,EAAE,EAAE;YAAEoC,OAAO,EAAE,MAAM;YAAES,cAAc,EAAE,eAAe;YAAER,UAAU,EAAE,QAAQ;YAAES,CAAC,EAAE;UAAE,CAAE;UAAA1C,QAAA,gBACxFxE,OAAA,CAACvB,UAAU;YAACqI,OAAO,EAAC,OAAO;YAAClC,KAAK,EAAC,gBAAgB;YAAAJ,QAAA,GAAC,UACzC,EAACrE,IAAI,GAAGE,WAAW,GAAG,CAAC,EAAC,MAAI,EAAC8G,IAAI,CAACC,GAAG,CAAC,CAACjH,IAAI,GAAG,CAAC,IAAIE,WAAW,EAAE2D,cAAc,CAACrB,MAAM,CAAC,EAAC,MAAI,EAACqB,cAAc,CAACrB,MAAM;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC,eACbjF,OAAA,CAACT,eAAe;YACd8H,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAE;YAChCf,SAAS,EAAC,KAAK;YACfgB,KAAK,EAAEtD,cAAc,CAACrB,MAAO;YAC7BtC,WAAW,EAAEA,WAAY;YACzBF,IAAI,EAAEA,IAAK;YACXoH,YAAY,EAAE9F,gBAAiB;YAC/B+F,mBAAmB,EAAE5F,uBAAwB;YAC7CwC,EAAE,EAAE;cACF,+BAA+B,EAAE;gBAC/BE,SAAS,EAAE;cACb,CAAC;cACD,wEAAwE,EAAE;gBACxEmD,MAAM,EAAE;cACV;YACF;UAAE;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGZjF,OAAA,CAACF,cAAc;MACb4H,IAAI,EAAE/G,aAAc;MACpBgH,OAAO,EAAE5E,oBAAqB;MAC9BD,KAAK,EAAEjC,aAAc;MACrB+G,MAAM,EAAE5E;IAAgB;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC/E,EAAA,CAvZID,SAAS;AAAA4H,EAAA,GAAT5H,SAAS;AAyZf,eAAeA,SAAS;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}