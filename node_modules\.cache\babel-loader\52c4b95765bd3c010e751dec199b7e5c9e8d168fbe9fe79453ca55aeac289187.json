{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Dash\\\\src\\\\components\\\\PropertyChart.js\";\nimport React from 'react';\nimport { Paper, Typography, Box } from '@mui/material';\nimport { BarChart } from '@mui/x-charts/BarChart';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PropertyChart = ({\n  title,\n  data,\n  height = 300,\n  isComparison = false\n}) => {\n  // Prepare data for the chart\n  const prepareChartData = () => {\n    if (isComparison) {\n      // For comparison charts (like Property Quick Filters)\n      return {\n        xAxis: [{\n          scaleType: 'band',\n          data: data.map(item => item.month),\n          categoryGapRatio: 0.3,\n          barGapRatio: 0.1\n        }],\n        series: [{\n          data: data.map(item => item.rent),\n          label: 'Rent',\n          color: '#e0e0e0'\n        }, {\n          data: data.map(item => item.sell),\n          label: 'Sell',\n          color: '#2196f3'\n        }]\n      };\n    } else {\n      // For single series charts (like Property Type) - show individual bars, not aggregated\n      const propertyColors = {\n        'Condo': '#e0e0e0',\n        'Single Family': '#e0e0e0',\n        'Apartment': '#2196f3',\n        // Highlight the apartment bar as shown in screenshot\n        'Land': '#e0e0e0'\n      };\n      return {\n        xAxis: [{\n          scaleType: 'band',\n          data: data.map((item, index) => {\n            // Create labels that match the screenshot\n            const typeLabels = {\n              'Condo': 'Condo',\n              'Single Family': 'Single Family',\n              'Apartment': 'Apartment',\n              'Land': 'Land'\n            };\n            return typeLabels[item.type] || item.type;\n          }),\n          categoryGapRatio: 0.2\n        }],\n        series: [{\n          data: data.map(item => item.value),\n          // Use a function to return different colors for different bars\n          color: params => {\n            const index = params.dataIndex;\n            const item = data[index];\n            if (item && item.type === 'Apartment' && item.value === 45) {\n              return '#2196f3'; // Blue for the highlighted apartment bar\n            }\n            return '#e0e0e0'; // Light gray for all other bars\n          }\n        }]\n      };\n    }\n  };\n  const chartConfig = prepareChartData();\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 3,\n      height: height + 80,\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n      borderRadius: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      component: \"h2\",\n      sx: {\n        mb: 2,\n        fontWeight: 600,\n        color: 'text.primary'\n      },\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        height: height\n      },\n      children: /*#__PURE__*/_jsxDEV(BarChart, {\n        xAxis: chartConfig.xAxis,\n        series: chartConfig.series,\n        width: undefined,\n        height: height,\n        margin: {\n          left: 50,\n          right: 50,\n          top: 20,\n          bottom: 50\n        },\n        slotProps: {\n          legend: isComparison ? {\n            direction: 'row',\n            position: {\n              vertical: 'top',\n              horizontal: 'right'\n            }\n          } : undefined\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n_c = PropertyChart;\nexport default PropertyChart;\nvar _c;\n$RefreshReg$(_c, \"PropertyChart\");", "map": {"version": 3, "names": ["React", "Paper", "Typography", "Box", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "PropertyChart", "title", "data", "height", "isComparison", "prepareChartData", "xAxis", "scaleType", "map", "item", "month", "categoryGapRatio", "barGapRatio", "series", "rent", "label", "color", "sell", "propertyColors", "index", "typeLabels", "type", "value", "params", "dataIndex", "chartConfig", "sx", "p", "boxShadow", "borderRadius", "children", "variant", "component", "mb", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "undefined", "margin", "left", "right", "top", "bottom", "slotProps", "legend", "direction", "position", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Dash/src/components/PropertyChart.js"], "sourcesContent": ["import React from 'react';\nimport { Paper, Typography, Box } from '@mui/material';\nimport { Bar<PERSON><PERSON> } from '@mui/x-charts/BarChart';\n\nconst PropertyChart = ({ title, data, height = 300, isComparison = false }) => {\n  // Prepare data for the chart\n  const prepareChartData = () => {\n    if (isComparison) {\n      // For comparison charts (like Property Quick Filters)\n      return {\n        xAxis: [{\n          scaleType: 'band',\n          data: data.map(item => item.month),\n          categoryGapRatio: 0.3,\n          barGapRatio: 0.1,\n        }],\n        series: [\n          {\n            data: data.map(item => item.rent),\n            label: 'Rent',\n            color: '#e0e0e0',\n          },\n          {\n            data: data.map(item => item.sell),\n            label: 'Sell',\n            color: '#2196f3',\n          },\n        ],\n      };\n    } else {\n      // For single series charts (like Property Type) - show individual bars, not aggregated\n      const propertyColors = {\n        'Condo': '#e0e0e0',\n        'Single Family': '#e0e0e0',\n        'Apartment': '#2196f3', // Highlight the apartment bar as shown in screenshot\n        'Land': '#e0e0e0'\n      };\n\n      return {\n        xAxis: [{\n          scaleType: 'band',\n          data: data.map((item, index) => {\n            // Create labels that match the screenshot\n            const typeLabels = {\n              'Condo': 'Condo',\n              'Single Family': 'Single Family',\n              'Apartment': 'Apartment',\n              'Land': 'Land'\n            };\n            return typeLabels[item.type] || item.type;\n          }),\n          categoryGapRatio: 0.2,\n        }],\n        series: [\n          {\n            data: data.map(item => item.value),\n            // Use a function to return different colors for different bars\n            color: (params) => {\n              const index = params.dataIndex;\n              const item = data[index];\n              if (item && item.type === 'Apartment' && item.value === 45) {\n                return '#2196f3'; // Blue for the highlighted apartment bar\n              }\n              return '#e0e0e0'; // Light gray for all other bars\n            },\n          },\n        ],\n      };\n    }\n  };\n\n  const chartConfig = prepareChartData();\n\n  return (\n    <Paper \n      sx={{ \n        p: 3, \n        height: height + 80,\n        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n        borderRadius: 2,\n      }}\n    >\n      <Typography \n        variant=\"h6\" \n        component=\"h2\" \n        sx={{ \n          mb: 2,\n          fontWeight: 600,\n          color: 'text.primary'\n        }}\n      >\n        {title}\n      </Typography>\n      <Box sx={{ width: '100%', height: height }}>\n        <BarChart\n          xAxis={chartConfig.xAxis}\n          series={chartConfig.series}\n          width={undefined}\n          height={height}\n          margin={{\n            left: 50,\n            right: 50,\n            top: 20,\n            bottom: 50,\n          }}\n          slotProps={{\n            legend: isComparison ? {\n              direction: 'row',\n              position: { vertical: 'top', horizontal: 'right' },\n            } : undefined,\n          }}\n        />\n      </Box>\n    </Paper>\n  );\n};\n\nexport default PropertyChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,UAAU,EAAEC,GAAG,QAAQ,eAAe;AACtD,SAASC,QAAQ,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,KAAK;EAAEC,IAAI;EAAEC,MAAM,GAAG,GAAG;EAAEC,YAAY,GAAG;AAAM,CAAC,KAAK;EAC7E;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAID,YAAY,EAAE;MAChB;MACA,OAAO;QACLE,KAAK,EAAE,CAAC;UACNC,SAAS,EAAE,MAAM;UACjBL,IAAI,EAAEA,IAAI,CAACM,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC;UAClCC,gBAAgB,EAAE,GAAG;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;QACFC,MAAM,EAAE,CACN;UACEX,IAAI,EAAEA,IAAI,CAACM,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACK,IAAI,CAAC;UACjCC,KAAK,EAAE,MAAM;UACbC,KAAK,EAAE;QACT,CAAC,EACD;UACEd,IAAI,EAAEA,IAAI,CAACM,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACQ,IAAI,CAAC;UACjCF,KAAK,EAAE,MAAM;UACbC,KAAK,EAAE;QACT,CAAC;MAEL,CAAC;IACH,CAAC,MAAM;MACL;MACA,MAAME,cAAc,GAAG;QACrB,OAAO,EAAE,SAAS;QAClB,eAAe,EAAE,SAAS;QAC1B,WAAW,EAAE,SAAS;QAAE;QACxB,MAAM,EAAE;MACV,CAAC;MAED,OAAO;QACLZ,KAAK,EAAE,CAAC;UACNC,SAAS,EAAE,MAAM;UACjBL,IAAI,EAAEA,IAAI,CAACM,GAAG,CAAC,CAACC,IAAI,EAAEU,KAAK,KAAK;YAC9B;YACA,MAAMC,UAAU,GAAG;cACjB,OAAO,EAAE,OAAO;cAChB,eAAe,EAAE,eAAe;cAChC,WAAW,EAAE,WAAW;cACxB,MAAM,EAAE;YACV,CAAC;YACD,OAAOA,UAAU,CAACX,IAAI,CAACY,IAAI,CAAC,IAAIZ,IAAI,CAACY,IAAI;UAC3C,CAAC,CAAC;UACFV,gBAAgB,EAAE;QACpB,CAAC,CAAC;QACFE,MAAM,EAAE,CACN;UACEX,IAAI,EAAEA,IAAI,CAACM,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACa,KAAK,CAAC;UAClC;UACAN,KAAK,EAAGO,MAAM,IAAK;YACjB,MAAMJ,KAAK,GAAGI,MAAM,CAACC,SAAS;YAC9B,MAAMf,IAAI,GAAGP,IAAI,CAACiB,KAAK,CAAC;YACxB,IAAIV,IAAI,IAAIA,IAAI,CAACY,IAAI,KAAK,WAAW,IAAIZ,IAAI,CAACa,KAAK,KAAK,EAAE,EAAE;cAC1D,OAAO,SAAS,CAAC,CAAC;YACpB;YACA,OAAO,SAAS,CAAC,CAAC;UACpB;QACF,CAAC;MAEL,CAAC;IACH;EACF,CAAC;EAED,MAAMG,WAAW,GAAGpB,gBAAgB,CAAC,CAAC;EAEtC,oBACEN,OAAA,CAACL,KAAK;IACJgC,EAAE,EAAE;MACFC,CAAC,EAAE,CAAC;MACJxB,MAAM,EAAEA,MAAM,GAAG,EAAE;MACnByB,SAAS,EAAE,2BAA2B;MACtCC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBAEF/B,OAAA,CAACJ,UAAU;MACToC,OAAO,EAAC,IAAI;MACZC,SAAS,EAAC,IAAI;MACdN,EAAE,EAAE;QACFO,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,GAAG;QACflB,KAAK,EAAE;MACT,CAAE;MAAAc,QAAA,EAED7B;IAAK;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACbvC,OAAA,CAACH,GAAG;MAAC8B,EAAE,EAAE;QAAEa,KAAK,EAAE,MAAM;QAAEpC,MAAM,EAAEA;MAAO,CAAE;MAAA2B,QAAA,eACzC/B,OAAA,CAACF,QAAQ;QACPS,KAAK,EAAEmB,WAAW,CAACnB,KAAM;QACzBO,MAAM,EAAEY,WAAW,CAACZ,MAAO;QAC3B0B,KAAK,EAAEC,SAAU;QACjBrC,MAAM,EAAEA,MAAO;QACfsC,MAAM,EAAE;UACNC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV,CAAE;QACFC,SAAS,EAAE;UACTC,MAAM,EAAE3C,YAAY,GAAG;YACrB4C,SAAS,EAAE,KAAK;YAChBC,QAAQ,EAAE;cAAEC,QAAQ,EAAE,KAAK;cAAEC,UAAU,EAAE;YAAQ;UACnD,CAAC,GAAGX;QACN;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACc,EAAA,GA/GIpD,aAAa;AAiHnB,eAAeA,aAAa;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}